<template>
  <div class="doctor-profile-form-overlay">
    <div class="doctor-profile-form-container">
      <div class="form-header">
        <h3>👨‍⚕️ 编辑个人资料</h3>
        <button @click="$emit('close')" class="close-btn">✕</button>
      </div>

      <div class="form-content">
        <!-- 头像区域 -->
        <div class="avatar-section">
          <div class="avatar-container">
            <div class="doctor-avatar" :style="{ backgroundImage: form.avatarUrl ? `url(${form.avatarUrl})` : 'none' }">
              <span v-if="!form.avatarUrl" class="avatar-placeholder">
                {{ form.nickname?.charAt(0) || userInfo?.nickname?.charAt(0) || 'D' }}
              </span>
            </div>
            <button @click="showAvatarInput = !showAvatarInput" class="change-avatar-btn">
              📷 更换头像
            </button>
          </div>
          
          <!-- 头像URL输入 -->
          <div v-if="showAvatarInput" class="avatar-input-section">
            <label for="avatarUrl">头像链接</label>
            <input
              id="avatarUrl"
              v-model="form.avatarUrl"
              type="url"
              placeholder="请输入头像图片链接"
              class="form-input"
              maxlength="255"
            />
            <span class="help-text">支持 jpg、png、gif 格式图片</span>
          </div>
        </div>

        <form @submit.prevent="handleSubmit" class="profile-form">
          <!-- 基础信息 -->
          <div class="form-section">
            <h4>基础信息</h4>
            
            <!-- 昵称 -->
            <div class="form-group">
              <label for="nickname">昵称 *</label>
              <input
                id="nickname"
                v-model="form.nickname"
                type="text"
                placeholder="请输入昵称"
                required
                maxlength="50"
                class="form-input"
              />
              <span class="help-text">{{ form.nickname?.length || 0 }}/50</span>
            </div>

            <!-- 真实姓名 -->
            <div class="form-group">
              <label for="realName">真实姓名 *</label>
              <input
                id="realName"
                v-model="form.realName"
                type="text"
                placeholder="请输入真实姓名"
                required
                maxlength="50"
                class="form-input"
              />
              <span class="help-text">医生执业姓名</span>
            </div>

            <!-- 手机号 -->
            <div class="form-group">
              <label for="phoneNumber">手机号</label>
              <input
                id="phoneNumber"
                v-model="form.phoneNumber"
                type="tel"
                placeholder="请输入手机号"
                pattern="^1[3-9]\d{9}$"
                maxlength="11"
                class="form-input"
              />
              <span class="help-text">修改手机号需要验证唯一性</span>
            </div>
          </div>

          <!-- 个人信息 -->
          <div class="form-section">
            <h4>个人信息</h4>
            
            <!-- 性别 -->
            <div class="form-group">
              <label for="gender">性别</label>
              <select id="gender" v-model="form.gender" class="form-select">
                <option value="">请选择性别</option>
                <option value="MALE">男</option>
                <option value="FEMALE">女</option>
                <option value="OTHER">其他</option>
              </select>
            </div>

            <!-- 出生日期 -->
            <div class="form-group">
              <label for="birthDate">出生日期</label>
              <input
                id="birthDate"
                v-model="form.birthDate"
                type="date"
                :max="maxBirthDate"
                class="form-input"
              />
              <span class="help-text">用于自动计算年龄</span>
            </div>

            <!-- 身份证号 -->
            <div class="form-group">
              <label for="idCardNumber">身份证号</label>
              <input
                id="idCardNumber"
                v-model="form.idCardNumber"
                type="text"
                placeholder="请输入身份证号"
                pattern="^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$"
                maxlength="18"
                class="form-input"
              />
              <span class="help-text">身份证号需要唯一性验证</span>
            </div>
          </div>

          <!-- 表单按钮 -->
          <div class="form-actions">
            <button type="button" @click="resetForm" class="btn-reset">
              重置
            </button>
            <button type="submit" :disabled="!isFormValid || loading" class="btn-submit">
              <span v-if="loading">保存中...</span>
              <span v-else>保存更改</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { getUserInfo, updateUserProfile } from '@/api/user'

// Props & Emits
const emit = defineEmits(['close', 'success'])

// 响应式数据
const userInfo = ref(null)
const loading = ref(false)
const showAvatarInput = ref(false)

const form = ref({
  nickname: '',
  realName: '',
  phoneNumber: '',
  gender: '',
  birthDate: '',
  idCardNumber: '',
  avatarUrl: ''
})

// 原始数据备份
const originalData = ref({})

// 计算属性
const maxBirthDate = computed(() => {
  const today = new Date()
  const maxDate = new Date(today.getFullYear() - 18, today.getMonth(), today.getDate())
  return maxDate.toISOString().split('T')[0]
})

const isFormValid = computed(() => {
  return form.value.nickname &&
         form.value.nickname.trim().length > 0 &&
         form.value.realName &&
         form.value.realName.trim().length > 0
})

const hasChanges = computed(() => {
  return JSON.stringify(form.value) !== JSON.stringify(originalData.value)
})

// 方法
const loadUserInfo = async () => {
  try {
    const response = await getUserInfo()
    if (response.data.code === 200) {
      userInfo.value = response.data.data
      
      // 填充表单数据
      form.value = {
        nickname: userInfo.value.nickname || '',
        realName: userInfo.value.realName || '',
        phoneNumber: userInfo.value.phoneNumber || '',
        gender: userInfo.value.gender || '',
        birthDate: userInfo.value.birthDate || '',
        idCardNumber: userInfo.value.idCardNumber || '',
        avatarUrl: userInfo.value.avatarUrl || ''
      }
      
      // 备份原始数据
      originalData.value = { ...form.value }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    alert('获取用户信息失败，请重试')
  }
}

// 重置表单
const resetForm = () => {
  form.value = { ...originalData.value }
  showAvatarInput.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!isFormValid.value || !hasChanges.value) return

  loading.value = true
  try {
    // 准备提交数据，只包含有变化的字段
    const submitData = {}
    
    Object.keys(form.value).forEach(key => {
      if (form.value[key] !== originalData.value[key]) {
        submitData[key] = form.value[key]
      }
    })

    const response = await updateUserProfile(submitData)
    
    if (response.data.code === 200) {
      // 更新原始数据
      originalData.value = { ...form.value }
      
      emit('success', '医生个人资料更新成功！')
    } else {
      alert('更新失败：' + response.data.message)
    }
  } catch (error) {
    console.error('更新医生个人资料失败:', error)
    if (error.response?.data?.message) {
      alert('更新失败：' + error.response.data.message)
    } else {
      alert('更新失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadUserInfo()
})
</script>

<style scoped>
.doctor-profile-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.doctor-profile-form-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12px 12px 0 0;
}

.form-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.form-content {
  padding: 24px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.doctor-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 40px;
  font-weight: 600;
}

.avatar-placeholder {
  text-transform: uppercase;
}

.change-avatar-btn {
  padding: 8px 16px;
  background: white;
  color: #3b82f6;
  border: 2px solid #3b82f6;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.change-avatar-btn:hover {
  background: #3b82f6;
  color: white;
}

.avatar-input-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.avatar-input-section label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-section h4 {
  margin: 0;
  color: #1e40af;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-input,
.form-select {
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.help-text {
  font-size: 12px;
  color: #6b7280;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 8px;
}

.btn-reset,
.btn-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-reset {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-reset:hover {
  background: #e5e7eb;
}

.btn-submit {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
}

.btn-submit:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-submit:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .doctor-profile-form-container {
    width: 95%;
    margin: 20px;
  }
  
  .form-content {
    padding: 16px;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
