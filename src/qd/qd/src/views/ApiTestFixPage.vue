<template>
  <div class="api-test-fix-page">
    <div class="page-header">
      <h1>🔧 API功能修复测试</h1>
      <p>测试修复后的问诊、处方、就诊记录功能</p>
    </div>

    <div class="test-sections">
      <!-- 1. 用户选择医生进行问诊测试 -->
      <div class="test-section">
        <h2>1. 用户选择医生进行问诊</h2>
        <div class="test-controls">
          <select v-model="selectedDoctorId" class="form-select">
            <option value="">选择医生</option>
            <option value="7">刘平安医生 (外科)</option>
            <option value="6">王健康医生 (内科)</option>
          </select>
          <button @click="testCreateConsultation" :disabled="!selectedDoctorId" class="test-btn">
            创建问诊会话
          </button>
        </div>
        <div v-if="consultationResult" class="test-result">
          <h4>测试结果:</h4>
          <pre>{{ JSON.stringify(consultationResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 2. 用户查看就诊记录测试 -->
      <div class="test-section">
        <h2>2. 用户查看就诊记录</h2>
        <div class="test-controls">
          <button @click="testGetMyAppointments" class="test-btn">
            获取我的就诊记录
          </button>
        </div>
        <div v-if="appointmentsResult" class="test-result">
          <h4>测试结果:</h4>
          <p>共找到 {{ appointmentsResult.totalElements }} 条就诊记录</p>
          <div v-for="appointment in appointmentsResult.content" :key="appointment.id" class="appointment-item">
            <p><strong>医生:</strong> {{ appointment.doctorName }}</p>
            <p><strong>科室:</strong> {{ appointment.departmentName }}</p>
            <p><strong>状态:</strong> {{ appointment.statusDescription }}</p>
            <p><strong>时间:</strong> {{ appointment.createdAt }}</p>
          </div>
        </div>
      </div>

      <!-- 3. 用户查看处方记录测试 -->
      <div class="test-section">
        <h2>3. 用户查看处方记录</h2>
        <div class="test-controls">
          <select v-model="selectedProfileId" class="form-select">
            <option value="">选择健康档案</option>
            <option value="18">张三的健康档案</option>
          </select>
          <button @click="testGetPrescriptions" :disabled="!selectedProfileId" class="test-btn">
            获取处方记录
          </button>
        </div>
        <div v-if="prescriptionsResult" class="test-result">
          <h4>测试结果:</h4>
          <p>共找到 {{ prescriptionsResult.totalElements }} 条处方记录</p>
          <div v-for="prescription in prescriptionsResult.content" :key="prescription.id" class="prescription-item">
            <p><strong>医生:</strong> {{ prescription.doctorName }}</p>
            <p><strong>诊断:</strong> {{ prescription.diagnosis }}</p>
            <p><strong>药品数量:</strong> {{ prescription.medications?.length || 0 }} 种</p>
            <p><strong>开具时间:</strong> {{ prescription.createdAt }}</p>
          </div>
        </div>
      </div>

      <!-- 4. 医生开具处方测试 -->
      <div class="test-section" v-if="userStore.isDoctor">
        <h2>4. 医生开具处方</h2>
        <div class="test-controls">
          <div class="form-group">
            <label>健康档案ID:</label>
            <input v-model="prescriptionForm.profileId" type="number" class="form-input" placeholder="18">
          </div>
          <div class="form-group">
            <label>问诊ID (可选):</label>
            <input v-model="prescriptionForm.consultationId" type="number" class="form-input" placeholder="4">
          </div>
          <div class="form-group">
            <label>诊断:</label>
            <input v-model="prescriptionForm.diagnosis" type="text" class="form-input" placeholder="感冒症状，建议休息">
          </div>
          <button @click="testCreatePrescription" class="test-btn">
            开具处方
          </button>
        </div>
        <div v-if="prescriptionCreateResult" class="test-result">
          <h4>测试结果:</h4>
          <pre>{{ JSON.stringify(prescriptionCreateResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 5. 医生完成诊疗测试 -->
      <div class="test-section" v-if="userStore.isDoctor">
        <h2>5. 医生完成诊疗</h2>
        <div class="test-controls">
          <input v-model="appointmentId" type="number" class="form-input" placeholder="预约ID">
          <button @click="testCompleteAppointment" :disabled="!appointmentId" class="test-btn">
            完成诊疗
          </button>
        </div>
        <div v-if="completeResult" class="test-result">
          <h4>测试结果:</h4>
          <pre>{{ JSON.stringify(completeResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 6. 医生列表测试 -->
      <div class="test-section">
        <h2>6. 医生列表加载测试</h2>
        <div class="test-controls">
          <button @click="testGetDoctorList" class="test-btn">
            获取医生列表
          </button>
        </div>
        <div v-if="doctorListResult" class="test-result">
          <h4>测试结果:</h4>
          <p v-if="doctorListResult.content">共找到 {{ doctorListResult.content.length }} 位医生</p>
          <div v-if="doctorListResult.content" class="doctor-list">
            <div v-for="doctor in doctorListResult.content.slice(0, 3)" :key="doctor.id || doctor.userId" class="doctor-item">
              <p><strong>姓名:</strong> {{ doctor.realName || doctor.nickname }}</p>
              <p><strong>科室:</strong> {{ doctor.departmentName }}</p>
              <p><strong>职称:</strong> {{ doctor.title }}</p>
              <p><strong>ID:</strong> {{ doctor.id || doctor.userId }}</p>
            </div>
          </div>
          <pre v-if="doctorListResult.error">{{ JSON.stringify(doctorListResult, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <!-- 测试日志 -->
    <div class="test-logs">
      <h3>测试日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in testLogs" :key="index" class="log-item" :class="log.type">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { createConsultation, getDoctorList } from '@/api/consultation'
import { getMyAppointments, completeAppointment } from '@/api/appointments'
import { getPatientPrescriptions, createPrescription } from '@/api/prescription'

const userStore = useUserStore()

// 响应式数据
const selectedDoctorId = ref('')
const selectedProfileId = ref('18') // 默认使用测试档案ID
const appointmentId = ref('')

// 测试结果
const consultationResult = ref(null)
const appointmentsResult = ref(null)
const prescriptionsResult = ref(null)
const prescriptionCreateResult = ref(null)
const completeResult = ref(null)
const doctorListResult = ref(null)

// 处方表单
const prescriptionForm = ref({
  profileId: 18,
  consultationId: 4,
  diagnosis: '感冒症状，建议休息',
  medications: [
    {
      name: '阿莫西林',
      specification: '500mg*12粒',
      dosage: '500mg',
      frequency: '每日3次',
      duration: '7天',
      quantity: 2,
      instructions: '饭后服用'
    }
  ]
})

// 测试日志
const testLogs = ref([])

const addLog = (message, type = 'info') => {
  testLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
}

// 测试方法
const testCreateConsultation = async () => {
  addLog('开始测试创建问诊会话...', 'info')
  try {
    const response = await createConsultation({ doctorId: parseInt(selectedDoctorId.value) })
    consultationResult.value = response.data
    addLog('创建问诊会话成功', 'success')
  } catch (error) {
    consultationResult.value = { error: error.message, details: error.response?.data }
    addLog('创建问诊会话失败: ' + error.message, 'error')
  }
}

const testGetMyAppointments = async () => {
  addLog('开始测试获取就诊记录...', 'info')
  try {
    const response = await getMyAppointments({ page: 1, size: 10 })
    appointmentsResult.value = response.data.data
    addLog('获取就诊记录成功', 'success')
  } catch (error) {
    appointmentsResult.value = { error: error.message, details: error.response?.data }
    addLog('获取就诊记录失败: ' + error.message, 'error')
  }
}

const testGetPrescriptions = async () => {
  addLog('开始测试获取处方记录...', 'info')
  try {
    const response = await getPatientPrescriptions(selectedProfileId.value, { page: 1, size: 10 })
    prescriptionsResult.value = response.data.data
    addLog('获取处方记录成功', 'success')
  } catch (error) {
    prescriptionsResult.value = { error: error.message, details: error.response?.data }
    addLog('获取处方记录失败: ' + error.message, 'error')
  }
}

const testCreatePrescription = async () => {
  addLog('开始测试开具处方...', 'info')
  try {
    const response = await createPrescription(prescriptionForm.value)
    prescriptionCreateResult.value = response.data
    addLog('开具处方成功', 'success')
  } catch (error) {
    prescriptionCreateResult.value = { error: error.message, details: error.response?.data }
    addLog('开具处方失败: ' + error.message, 'error')
  }
}

const testCompleteAppointment = async () => {
  addLog('开始测试完成诊疗...', 'info')
  try {
    const response = await completeAppointment(appointmentId.value, { notes: '诊疗完成，患者恢复良好' })
    completeResult.value = response.data
    addLog('完成诊疗成功', 'success')
  } catch (error) {
    completeResult.value = { error: error.message, details: error.response?.data }
    addLog('完成诊疗失败: ' + error.message, 'error')
  }
}

const testGetDoctorList = async () => {
  addLog('开始测试获取医生列表...', 'info')
  try {
    const response = await getDoctorList({ page: 1, size: 20 })
    doctorListResult.value = response.data.data
    addLog('获取医生列表成功', 'success')
  } catch (error) {
    doctorListResult.value = { error: error.message, details: error.response?.data }
    addLog('获取医生列表失败: ' + error.message, 'error')
  }
}

onMounted(() => {
  addLog('API功能修复测试页面已加载', 'info')
  addLog('当前用户角色: ' + (userStore.isDoctor ? '医生' : userStore.isResident ? '居民' : '管理员'), 'info')
})
</script>

<style scoped>
.api-test-fix-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.test-sections {
  display: grid;
  gap: 24px;
  margin-bottom: 32px;
}

.test-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
  border-bottom: 2px solid #f1f5f9;
  padding-bottom: 8px;
}

.test-controls {
  display: flex;
  gap: 12px;
  align-items: end;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-select,
.form-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  min-width: 150px;
}

.test-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.test-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.test-result {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-top: 16px;
}

.test-result h4 {
  margin: 0 0 8px 0;
  color: #374151;
}

.test-result pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 12px;
  border-radius: 6px;
  font-size: 12px;
  overflow-x: auto;
  max-height: 300px;
}

.appointment-item,
.prescription-item,
.doctor-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.appointment-item p,
.prescription-item p,
.doctor-item p {
  margin: 4px 0;
  font-size: 14px;
}

.doctor-list {
  max-height: 300px;
  overflow-y: auto;
}

.test-logs {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-logs h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  background: #f9fafb;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 4px 0;
  font-size: 14px;
  font-family: 'Courier New', monospace;
}

.log-time {
  color: #6b7280;
  min-width: 80px;
}

.log-item.success .log-message {
  color: #059669;
}

.log-item.error .log-message {
  color: #dc2626;
}

.log-item.info .log-message {
  color: #374151;
}
</style>
