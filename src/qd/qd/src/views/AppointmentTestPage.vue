<template>
  <div class="appointment-test-page">
    <div class="page-header">
      <h1>预约功能测试页面</h1>
      <p>测试用户端预约功能的各个API接口</p>
    </div>

    <div class="test-sections">
      <!-- 科室列表测试 -->
      <div class="test-section">
        <h3>1. 科室列表测试</h3>
        <button @click="testGetDepartments" :disabled="loading.departments" class="test-btn">
          {{ loading.departments ? '测试中...' : '测试获取科室列表' }}
        </button>
        <div v-if="results.departments" class="test-result">
          <h4>结果:</h4>
          <pre>{{ JSON.stringify(results.departments, null, 2) }}</pre>
        </div>
      </div>

      <!-- 医生搜索测试 -->
      <div class="test-section">
        <h3>2. 医生搜索测试</h3>
        <div class="test-controls">
          <input v-model="searchParams.name" placeholder="医生姓名" class="test-input">
          <button @click="testSearchDoctors" :disabled="loading.doctors" class="test-btn">
            {{ loading.doctors ? '测试中...' : '测试搜索医生' }}
          </button>
        </div>
        <div v-if="results.doctors" class="test-result">
          <h4>结果:</h4>
          <pre>{{ JSON.stringify(results.doctors, null, 2) }}</pre>
        </div>
      </div>

      <!-- 科室医生测试 -->
      <div class="test-section">
        <h3>3. 科室医生测试</h3>
        <div class="test-controls">
          <select v-model="selectedDepartmentId" class="test-select">
            <option value="">选择科室</option>
            <option v-for="dept in departments" :key="dept.id" :value="dept.id">
              {{ dept.name }}
            </option>
          </select>
          <button @click="testGetDepartmentDoctors" :disabled="loading.departmentDoctors || !selectedDepartmentId" class="test-btn">
            {{ loading.departmentDoctors ? '测试中...' : '测试获取科室医生' }}
          </button>
        </div>
        <div v-if="results.departmentDoctors" class="test-result">
          <h4>结果:</h4>
          <pre>{{ JSON.stringify(results.departmentDoctors, null, 2) }}</pre>
        </div>
      </div>

      <!-- 医生详情测试 -->
      <div class="test-section">
        <h3>4. 医生详情测试</h3>
        <div class="test-controls">
          <select v-model="selectedDoctorId" class="test-select">
            <option value="">选择医生</option>
            <option v-for="doctor in doctorsList" :key="doctor.userId" :value="doctor.userId">
              {{ doctor.realName }} - {{ doctor.title }}
            </option>
          </select>
          <button @click="testGetDoctorDetail" :disabled="loading.doctorDetail || !selectedDoctorId" class="test-btn">
            {{ loading.doctorDetail ? '测试中...' : '测试获取医生详情' }}
          </button>
        </div>
        <div v-if="results.doctorDetail" class="test-result">
          <h4>结果:</h4>
          <pre>{{ JSON.stringify(results.doctorDetail, null, 2) }}</pre>
        </div>
      </div>

      <!-- 医生排班测试 -->
      <div class="test-section">
        <h3>5. 医生排班测试</h3>
        <div class="test-controls">
          <input v-model="scheduleParams.startDate" type="date" class="test-input">
          <input v-model="scheduleParams.endDate" type="date" class="test-input">
          <button @click="testGetDoctorSchedules" :disabled="loading.schedules || !selectedDoctorId" class="test-btn">
            {{ loading.schedules ? '测试中...' : '测试获取医生排班' }}
          </button>
        </div>
        <div v-if="results.schedules" class="test-result">
          <h4>结果:</h4>
          <pre>{{ JSON.stringify(results.schedules, null, 2) }}</pre>
        </div>
      </div>

      <!-- 我的预约测试 -->
      <div class="test-section">
        <h3>6. 我的预约测试</h3>
        <button @click="testGetMyAppointments" :disabled="loading.myAppointments" class="test-btn">
          {{ loading.myAppointments ? '测试中...' : '测试获取我的预约' }}
        </button>
        <div v-if="results.myAppointments" class="test-result">
          <h4>结果:</h4>
          <pre>{{ JSON.stringify(results.myAppointments, null, 2) }}</pre>
        </div>
      </div>

      <!-- 即将到来的预约测试 -->
      <div class="test-section">
        <h3>7. 即将到来的预约测试</h3>
        <button @click="testGetUpcomingAppointments" :disabled="loading.upcomingAppointments" class="test-btn">
          {{ loading.upcomingAppointments ? '测试中...' : '测试获取即将到来的预约' }}
        </button>
        <div v-if="results.upcomingAppointments" class="test-result">
          <h4>结果:</h4>
          <pre>{{ JSON.stringify(results.upcomingAppointments, null, 2) }}</pre>
        </div>
      </div>

      <!-- 健康档案测试 -->
      <div class="test-section">
        <h3>8. 健康档案测试</h3>
        <button @click="testGetHealthProfiles" :disabled="loading.healthProfiles" class="test-btn">
          {{ loading.healthProfiles ? '测试中...' : '测试获取健康档案' }}
        </button>
        <div v-if="results.healthProfiles" class="test-result">
          <h4>结果:</h4>
          <pre>{{ JSON.stringify(results.healthProfiles, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import * as appointmentsApi from '@/api/appointments'
import * as healthApi from '@/api/health'

export default {
  name: 'AppointmentTestPage',
  setup() {
    // 响应式数据
    const loading = reactive({
      departments: false,
      doctors: false,
      departmentDoctors: false,
      doctorDetail: false,
      schedules: false,
      myAppointments: false,
      upcomingAppointments: false,
      healthProfiles: false
    })

    const results = reactive({
      departments: null,
      doctors: null,
      departmentDoctors: null,
      doctorDetail: null,
      schedules: null,
      myAppointments: null,
      upcomingAppointments: null,
      healthProfiles: null
    })

    const searchParams = reactive({
      name: '王'
    })

    const scheduleParams = reactive({
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    })

    const selectedDepartmentId = ref('')
    const selectedDoctorId = ref('')

    // 计算属性
    const departments = computed(() => {
      return results.departments?.data || []
    })

    const doctorsList = computed(() => {
      const searchResults = results.doctors?.data?.content || []
      const deptResults = results.departmentDoctors?.data?.content || []
      return [...searchResults, ...deptResults]
    })

    // 测试方法
    const testGetDepartments = async () => {
      loading.departments = true
      try {
        const response = await appointmentsApi.getDepartments()
        results.departments = response.data
        console.log('科室列表测试结果:', response.data)
      } catch (error) {
        console.error('科室列表测试失败:', error)
        results.departments = { error: error.message }
      } finally {
        loading.departments = false
      }
    }

    const testSearchDoctors = async () => {
      loading.doctors = true
      try {
        const params = {
          name: searchParams.name,
          page: 0,
          size: 10
        }
        const response = await appointmentsApi.searchDoctors(params)
        results.doctors = response.data
        console.log('医生搜索测试结果:', response.data)
      } catch (error) {
        console.error('医生搜索测试失败:', error)
        results.doctors = { error: error.message }
      } finally {
        loading.doctors = false
      }
    }

    const testGetDepartmentDoctors = async () => {
      loading.departmentDoctors = true
      try {
        const params = { page: 0, size: 10 }
        const response = await appointmentsApi.getDepartmentDoctors(selectedDepartmentId.value, params)
        results.departmentDoctors = response.data
        console.log('科室医生测试结果:', response.data)
      } catch (error) {
        console.error('科室医生测试失败:', error)
        results.departmentDoctors = { error: error.message }
      } finally {
        loading.departmentDoctors = false
      }
    }

    const testGetDoctorDetail = async () => {
      loading.doctorDetail = true
      try {
        const response = await appointmentsApi.getDoctorDetail(selectedDoctorId.value)
        results.doctorDetail = response.data
        console.log('医生详情测试结果:', response.data)
      } catch (error) {
        console.error('医生详情测试失败:', error)
        results.doctorDetail = { error: error.message }
      } finally {
        loading.doctorDetail = false
      }
    }

    const testGetDoctorSchedules = async () => {
      loading.schedules = true
      try {
        const params = {
          startDate: scheduleParams.startDate,
          endDate: scheduleParams.endDate
        }
        const response = await appointmentsApi.getDoctorSchedules(selectedDoctorId.value, params)
        results.schedules = response.data
        console.log('医生排班测试结果:', response.data)
      } catch (error) {
        console.error('医生排班测试失败:', error)
        results.schedules = { error: error.message }
      } finally {
        loading.schedules = false
      }
    }

    const testGetMyAppointments = async () => {
      loading.myAppointments = true
      try {
        const params = { page: 0, size: 10 }
        const response = await appointmentsApi.getMyAppointments(params)
        results.myAppointments = response.data
        console.log('我的预约测试结果:', response.data)
      } catch (error) {
        console.error('我的预约测试失败:', error)
        results.myAppointments = { error: error.message }
      } finally {
        loading.myAppointments = false
      }
    }

    const testGetUpcomingAppointments = async () => {
      loading.upcomingAppointments = true
      try {
        const params = { days: 7 }
        const response = await appointmentsApi.getUpcomingAppointments(params)
        results.upcomingAppointments = response.data
        console.log('即将到来的预约测试结果:', response.data)
      } catch (error) {
        console.error('即将到来的预约测试失败:', error)
        results.upcomingAppointments = { error: error.message }
      } finally {
        loading.upcomingAppointments = false
      }
    }

    const testGetHealthProfiles = async () => {
      loading.healthProfiles = true
      try {
        const response = await healthApi.getHealthProfiles()
        results.healthProfiles = response.data
        console.log('健康档案测试结果:', response.data)
      } catch (error) {
        console.error('健康档案测试失败:', error)
        results.healthProfiles = { error: error.message }
      } finally {
        loading.healthProfiles = false
      }
    }

    // 组件挂载时自动测试科室列表和健康档案
    onMounted(() => {
      testGetDepartments()
      testGetHealthProfiles()
    })

    return {
      loading,
      results,
      searchParams,
      scheduleParams,
      selectedDepartmentId,
      selectedDoctorId,
      departments,
      doctorsList,
      testGetDepartments,
      testSearchDoctors,
      testGetDepartmentDoctors,
      testGetDoctorDetail,
      testGetDoctorSchedules,
      testGetMyAppointments,
      testGetUpcomingAppointments,
      testGetHealthProfiles
    }
  }
}
</script>

<style scoped>
.appointment-test-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
}

.page-header p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.test-section {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.test-section h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.test-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 10px 20px;
  background: #4A90E2;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.3s;
}

.test-btn:hover:not(:disabled) {
  background: #357abd;
}

.test-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.test-input, .test-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-width: 150px;
}

.test-result {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  margin-top: 15px;
}

.test-result h4 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.test-result pre {
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .test-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .test-input, .test-select {
    min-width: auto;
  }
}
</style>
