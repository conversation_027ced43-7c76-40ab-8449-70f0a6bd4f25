<template>
  <div class="api-test-validation">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>API测试报告验证</h1>
      <p class="page-description">验证前端功能与API测试报告的一致性</p>
    </div>

    <!-- 用户信息 -->
    <div class="user-info">
      <h2>当前用户信息</h2>
      <div class="user-details">
        <p><strong>用户ID:</strong> {{ userStore.userInfo?.id }}</p>
        <p><strong>昵称:</strong> {{ userStore.userInfo?.nickname }}</p>
        <p><strong>手机号:</strong> {{ userStore.userInfo?.phoneNumber }}</p>
        <p><strong>角色:</strong> {{ getUserRole() }}</p>
        <p><strong>Token:</strong> {{ userStore.token ? '已设置' : '未设置' }}</p>
      </div>
    </div>

    <!-- 测试功能列表 -->
    <div class="test-functions">
      <h2>API测试报告中的5个核心功能</h2>
      
      <!-- 1. 用户选择医生进行问诊 -->
      <div class="test-item">
        <h3>1. 用户选择医生进行问诊 (POST /api/consultations)</h3>
        <p class="test-desc">测试居民用户向指定医生发起问诊会话</p>
        <div class="test-controls">
          <select v-model="selectedDoctorId" class="doctor-select">
            <option value="">选择医生</option>
            <option value="7">刘平安医生 (ID: 7)</option>
            <option value="6">王健康主任医师 (ID: 6)</option>
          </select>
          <button 
            @click="testCreateConsultation" 
            :disabled="!selectedDoctorId || testing.consultation"
            class="test-btn"
          >
            {{ testing.consultation ? '测试中...' : '测试问诊创建' }}
          </button>
        </div>
        <div v-if="results.consultation" class="test-result">
          <h4>测试结果:</h4>
          <pre>{{ JSON.stringify(results.consultation, null, 2) }}</pre>
        </div>
      </div>

      <!-- 2. 用户查看就诊记录 -->
      <div class="test-item">
        <h3>2. 用户查看就诊记录 (GET /api/appointments/my)</h3>
        <p class="test-desc">测试居民用户查看自己的预约记录</p>
        <div class="test-controls">
          <button 
            @click="testGetMyAppointments" 
            :disabled="testing.appointments"
            class="test-btn"
          >
            {{ testing.appointments ? '测试中...' : '测试就诊记录' }}
          </button>
        </div>
        <div v-if="results.appointments" class="test-result">
          <h4>测试结果:</h4>
          <pre>{{ JSON.stringify(results.appointments, null, 2) }}</pre>
        </div>
      </div>

      <!-- 3. 用户筛选查看处方 -->
      <div class="test-item">
        <h3>3. 用户筛选查看处方 (GET /api/profiles/{profileId}/prescriptions)</h3>
        <p class="test-desc">测试居民用户查看健康档案的处方记录</p>
        <div class="test-controls">
          <input 
            v-model="testProfileId" 
            type="number" 
            placeholder="健康档案ID (如: 18)"
            class="profile-input"
          >
          <button 
            @click="testGetPrescriptions" 
            :disabled="!testProfileId || testing.prescriptions"
            class="test-btn"
          >
            {{ testing.prescriptions ? '测试中...' : '测试处方查看' }}
          </button>
        </div>
        <div v-if="results.prescriptions" class="test-result">
          <h4>测试结果:</h4>
          <pre>{{ JSON.stringify(results.prescriptions, null, 2) }}</pre>
        </div>
      </div>

      <!-- 4. 医生对患者预约进行就诊 -->
      <div class="test-item">
        <h3>4. 医生对患者预约进行就诊 (POST /api/doctor/appointments/{id}/complete)</h3>
        <p class="test-desc">测试医生完成患者诊疗</p>
        <div class="test-controls">
          <input 
            v-model="testAppointmentId" 
            type="number" 
            placeholder="预约ID"
            class="appointment-input"
          >
          <input 
            v-model="treatmentNotes" 
            type="text" 
            placeholder="诊疗备注"
            class="notes-input"
          >
          <button 
            @click="testCompleteAppointment" 
            :disabled="!testAppointmentId || testing.complete"
            class="test-btn"
          >
            {{ testing.complete ? '测试中...' : '测试完成诊疗' }}
          </button>
        </div>
        <div v-if="results.complete" class="test-result">
          <h4>测试结果:</h4>
          <pre>{{ JSON.stringify(results.complete, null, 2) }}</pre>
        </div>
      </div>

      <!-- 5. 医生开具处方获取患者信息 -->
      <div class="test-item">
        <h3>5. 医生开具处方获取患者信息 (POST /api/prescriptions)</h3>
        <p class="test-desc">测试医生为患者开具电子处方</p>
        <div class="test-controls">
          <input 
            v-model="prescriptionData.profileId" 
            type="number" 
            placeholder="健康档案ID (如: 18)"
            class="profile-input"
          >
          <input 
            v-model="prescriptionData.consultationId" 
            type="number" 
            placeholder="问诊ID (可选)"
            class="consultation-input"
          >
          <input 
            v-model="prescriptionData.diagnosis" 
            type="text" 
            placeholder="诊断信息"
            class="diagnosis-input"
          >
          <button 
            @click="testCreatePrescription" 
            :disabled="!prescriptionData.profileId || !prescriptionData.diagnosis || testing.prescription"
            class="test-btn"
          >
            {{ testing.prescription ? '测试中...' : '测试开具处方' }}
          </button>
        </div>
        <div v-if="results.prescription" class="test-result">
          <h4>测试结果:</h4>
          <pre>{{ JSON.stringify(results.prescription, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <!-- 测试总结 -->
    <div class="test-summary">
      <h2>测试总结</h2>
      <div class="summary-stats">
        <div class="stat-item">
          <div class="stat-number">{{ completedTests }}</div>
          <div class="stat-label">已完成测试</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ successfulTests }}</div>
          <div class="stat-label">成功测试</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ failedTests }}</div>
          <div class="stat-label">失败测试</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { createConsultation } from '@/api/consultation'
import { getMyAppointments, completeAppointment } from '@/api/appointments'
import { getPatientPrescriptions, createPrescription } from '@/api/prescription'

const userStore = useUserStore()

// 测试数据
const selectedDoctorId = ref('')
const testProfileId = ref('18')
const testAppointmentId = ref('')
const treatmentNotes = ref('诊疗完成，患者恢复良好')

const prescriptionData = ref({
  profileId: 18,
  consultationId: null,
  diagnosis: '感冒症状，建议休息'
})

// 测试状态
const testing = ref({
  consultation: false,
  appointments: false,
  prescriptions: false,
  complete: false,
  prescription: false
})

// 测试结果
const results = ref({
  consultation: null,
  appointments: null,
  prescriptions: null,
  complete: null,
  prescription: null
})

// 计算属性
const completedTests = computed(() => {
  return Object.values(results.value).filter(result => result !== null).length
})

const successfulTests = computed(() => {
  return Object.values(results.value).filter(result => result?.success === true).length
})

const failedTests = computed(() => {
  return Object.values(results.value).filter(result => result?.success === false).length
})

// 方法
const getUserRole = () => {
  if (userStore.isAdmin) return '管理员'
  if (userStore.isDoctor) return '医生'
  if (userStore.isResident) return '居民'
  return '未知'
}

// 测试功能
const testCreateConsultation = async () => {
  testing.value.consultation = true
  try {
    const response = await createConsultation({ doctorId: parseInt(selectedDoctorId.value) })
    results.value.consultation = {
      success: response.data.code === 200,
      data: response.data,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    results.value.consultation = {
      success: false,
      error: error.response?.data || error.message,
      timestamp: new Date().toISOString()
    }
  } finally {
    testing.value.consultation = false
  }
}

const testGetMyAppointments = async () => {
  testing.value.appointments = true
  try {
    const response = await getMyAppointments({ page: 1, size: 10 })
    results.value.appointments = {
      success: response.data.code === 200,
      data: response.data,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    results.value.appointments = {
      success: false,
      error: error.response?.data || error.message,
      timestamp: new Date().toISOString()
    }
  } finally {
    testing.value.appointments = false
  }
}

const testGetPrescriptions = async () => {
  testing.value.prescriptions = true
  try {
    const response = await getPatientPrescriptions(testProfileId.value, { page: 1, size: 10 })
    results.value.prescriptions = {
      success: response.data.code === 200,
      data: response.data,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    results.value.prescriptions = {
      success: false,
      error: error.response?.data || error.message,
      timestamp: new Date().toISOString()
    }
  } finally {
    testing.value.prescriptions = false
  }
}

const testCompleteAppointment = async () => {
  testing.value.complete = true
  try {
    const response = await completeAppointment(testAppointmentId.value, { notes: treatmentNotes.value })
    results.value.complete = {
      success: response.data.code === 200,
      data: response.data,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    results.value.complete = {
      success: false,
      error: error.response?.data || error.message,
      timestamp: new Date().toISOString()
    }
  } finally {
    testing.value.complete = false
  }
}

const testCreatePrescription = async () => {
  testing.value.prescription = true
  try {
    const requestData = {
      profileId: parseInt(prescriptionData.value.profileId),
      diagnosis: prescriptionData.value.diagnosis,
      medications: [
        {
          name: '阿莫西林',
          specification: '500mg*12粒',
          dosage: '500mg',
          frequency: '每日3次',
          duration: '7天',
          quantity: 2,
          instructions: '饭后服用'
        }
      ]
    }
    
    if (prescriptionData.value.consultationId) {
      requestData.consultationId = parseInt(prescriptionData.value.consultationId)
    }
    
    const response = await createPrescription(requestData)
    results.value.prescription = {
      success: response.data.code === 200,
      data: response.data,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    results.value.prescription = {
      success: false,
      error: error.response?.data || error.message,
      timestamp: new Date().toISOString()
    }
  } finally {
    testing.value.prescription = false
  }
}
</script>

<style scoped>
.api-test-validation {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.page-description {
  color: #64748b;
  font-size: 16px;
  margin: 0;
}

.user-info {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 32px;
}

.user-info h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.user-details p {
  margin: 8px 0;
  color: #374151;
}

.test-functions {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 32px;
}

.test-functions h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 24px 0;
}

.test-item {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

.test-item h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.test-desc {
  color: #64748b;
  margin: 0 0 16px 0;
}

.test-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.doctor-select,
.profile-input,
.appointment-input,
.notes-input,
.consultation-input,
.diagnosis-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.test-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-btn:hover:not(:disabled) {
  background: #2563eb;
}

.test-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.test-result {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
}

.test-result h4 {
  margin: 0 0 12px 0;
  color: #1e293b;
}

.test-result pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 12px;
  margin: 0;
}

.test-summary {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-summary h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
}

.summary-stats {
  display: flex;
  gap: 24px;
  justify-content: center;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
}
</style>
