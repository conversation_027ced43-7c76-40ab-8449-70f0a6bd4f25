<template>
  <div class="medical-record-form-overlay">
    <div class="medical-record-form-container">
      <div class="form-header">
        <h3>📝 添加诊疗记录</h3>
        <button @click="$emit('close')" class="close-btn">✕</button>
      </div>

      <div class="form-content">
        <!-- 患者信息展示 -->
        <div class="patient-summary">
          <div class="patient-info">
            <div class="patient-avatar">
              <span class="avatar-text">
                {{ getPatientInitial(appointment?.patient) }}
              </span>
            </div>
            <div class="patient-details">
              <div class="patient-name">{{ getPatientName(appointment?.patient) }}</div>
              <div class="patient-meta">
                {{ getPatientAge(appointment?.patient?.birthDate) }}岁 
                {{ getPatientGender(appointment?.patient?.gender) }}
              </div>
              <div class="appointment-time">
                {{ formatAppointmentDate(appointment?.appointmentDate) }} 
                {{ formatTimeDisplay(appointment?.appointmentTime) }}
              </div>
            </div>
          </div>
        </div>

        <form @submit.prevent="handleSubmit" class="record-form">
          <!-- 诊断结果 -->
          <div class="form-group">
            <label for="diagnosis">诊断结果 *</label>
            <textarea
              id="diagnosis"
              v-model="form.diagnosis"
              placeholder="请输入详细的诊断结果..."
              required
              rows="4"
              maxlength="500"
              class="form-textarea"
              :class="{ 'error': errors.diagnosis }"
            ></textarea>
            <div class="field-footer">
              <span v-if="errors.diagnosis" class="error-text">{{ errors.diagnosis }}</span>
              <span class="char-count">{{ form.diagnosis.length }}/500</span>
            </div>
          </div>

          <!-- 治疗方案 -->
          <div class="form-group">
            <label for="treatment">治疗方案 *</label>
            <textarea
              id="treatment"
              v-model="form.treatment"
              placeholder="请输入详细的治疗方案..."
              required
              rows="5"
              maxlength="1000"
              class="form-textarea"
              :class="{ 'error': errors.treatment }"
            ></textarea>
            <div class="field-footer">
              <span v-if="errors.treatment" class="error-text">{{ errors.treatment }}</span>
              <span class="char-count">{{ form.treatment.length }}/1000</span>
            </div>
          </div>

          <!-- 处方信息 -->
          <div class="form-group">
            <label for="prescription">处方信息</label>
            <textarea
              id="prescription"
              v-model="form.prescription"
              placeholder="请输入处方信息（药品名称、用法用量等）..."
              rows="4"
              maxlength="1000"
              class="form-textarea"
              :class="{ 'error': errors.prescription }"
            ></textarea>
            <div class="field-footer">
              <span v-if="errors.prescription" class="error-text">{{ errors.prescription }}</span>
              <span class="char-count">{{ form.prescription.length }}/1000</span>
            </div>
            <span class="help-text">可选项，如无处方可留空</span>
          </div>

          <!-- 快捷模板 -->
          <div class="template-section">
            <h4>📋 快捷模板</h4>
            <div class="template-buttons">
              <button
                type="button"
                v-for="template in templates"
                :key="template.name"
                @click="applyTemplate(template)"
                class="template-btn"
              >
                {{ template.name }}
              </button>
            </div>
          </div>

          <!-- 表单按钮 -->
          <div class="form-actions">
            <button type="button" @click="resetForm" class="btn-reset">
              重置
            </button>
            <button type="submit" :disabled="!isFormValid || loading" class="btn-submit">
              <span v-if="loading">保存中...</span>
              <span v-else>保存记录</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { addMedicalRecord } from '@/api/appointments'
import { validateMedicalRecord, getPatientAge, formatAppointmentDate } from '@/utils/appointmentUtils'
import { formatTimeDisplay } from '@/utils/dateUtils'

// Props & Emits
const props = defineProps({
  appointment: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'success'])

// 响应式数据
const loading = ref(false)

const form = ref({
  diagnosis: '',
  treatment: '',
  prescription: ''
})

const errors = ref({
  diagnosis: '',
  treatment: '',
  prescription: ''
})

// 诊疗记录模板
const templates = ref([
  {
    name: '感冒',
    diagnosis: '急性上呼吸道感染',
    treatment: '1. 多休息，保证充足睡眠\n2. 多饮温开水\n3. 清淡饮食\n4. 避免受凉',
    prescription: '阿莫西林胶囊 0.5g 每日3次，饭后服用，连服5-7天\n复方氨酚烷胺片 1片 每日3次，症状缓解后停药'
  },
  {
    name: '高血压',
    diagnosis: '原发性高血压',
    treatment: '1. 低盐低脂饮食\n2. 适量运动\n3. 控制体重\n4. 戒烟限酒\n5. 规律作息\n6. 定期监测血压',
    prescription: '苯磺酸氨氯地平片 5mg 每日1次，晨起服用\n定期复查血压，根据血压情况调整用药'
  },
  {
    name: '糖尿病',
    diagnosis: '2型糖尿病',
    treatment: '1. 糖尿病饮食控制\n2. 规律运动\n3. 血糖监测\n4. 足部护理\n5. 定期复查',
    prescription: '二甲双胍片 0.5g 每日2次，餐后服用\n定期监测血糖，根据血糖情况调整治疗方案'
  },
  {
    name: '胃炎',
    diagnosis: '慢性浅表性胃炎',
    treatment: '1. 规律饮食，少食多餐\n2. 避免辛辣刺激食物\n3. 戒烟戒酒\n4. 减少精神压力',
    prescription: '奥美拉唑肠溶胶囊 20mg 每日1次，餐前30分钟服用\n铝碳酸镁片 1g 每日3次，餐后1小时服用'
  }
])

// 计算属性
const isFormValid = computed(() => {
  return form.value.diagnosis.trim().length > 0 &&
         form.value.treatment.trim().length > 0 &&
         !Object.values(errors.value).some(error => error)
})

// 方法
const getPatientName = (patient) => {
  return patient?.name || patient?.profileOwnerName || '未知患者'
}

const getPatientInitial = (patient) => {
  const name = getPatientName(patient)
  return name.charAt(0).toUpperCase()
}

const getPatientGender = (gender) => {
  const genderMap = {
    'male': '男',
    'female': '女',
    'MALE': '男',
    'FEMALE': '女',
    'OTHER': '其他'
  }
  return genderMap[gender] || ''
}

const validateForm = () => {
  const validation = validateMedicalRecord(form.value)
  errors.value = validation.errors
  return validation.isValid
}

const applyTemplate = (template) => {
  form.value.diagnosis = template.diagnosis
  form.value.treatment = template.treatment
  form.value.prescription = template.prescription
  validateForm()
}

const resetForm = () => {
  form.value = {
    diagnosis: '',
    treatment: '',
    prescription: ''
  }
  errors.value = {
    diagnosis: '',
    treatment: '',
    prescription: ''
  }
}

const handleSubmit = async () => {
  if (!validateForm()) return

  loading.value = true
  try {
    const recordData = {
      diagnosis: form.value.diagnosis.trim(),
      treatment: form.value.treatment.trim(),
      prescription: form.value.prescription.trim() || null
    }

    const response = await addMedicalRecord(props.appointment.id, recordData)
    
    if (response.data.code === 200) {
      emit('success', '诊疗记录添加成功！')
      resetForm()
    } else {
      alert('添加失败：' + response.data.message)
    }
  } catch (error) {
    console.error('添加诊疗记录失败:', error)
    if (error.response?.data?.message) {
      alert('添加失败：' + error.response.data.message)
    } else {
      alert('添加失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  // 可以在这里预填一些默认信息
})
</script>

<style scoped>
.medical-record-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.medical-record-form-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12px 12px 0 0;
}

.form-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.form-content {
  padding: 24px;
}

.patient-summary {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.patient-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  font-weight: 600;
  flex-shrink: 0;
}

.patient-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.patient-meta {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.appointment-time {
  font-size: 13px;
  color: #3b82f6;
  font-weight: 500;
}

.record-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-textarea {
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  transition: border-color 0.2s, box-shadow 0.2s;
  line-height: 1.5;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea.error {
  border-color: #ef4444;
}

.field-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-text {
  font-size: 12px;
  color: #ef4444;
}

.char-count {
  font-size: 12px;
  color: #6b7280;
}

.help-text {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.template-section {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 16px;
}

.template-section h4 {
  margin: 0 0 12px 0;
  color: #1e40af;
  font-size: 16px;
  font-weight: 600;
}

.template-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
}

.template-btn {
  padding: 8px 12px;
  background: white;
  border: 1px solid #3b82f6;
  border-radius: 6px;
  color: #3b82f6;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  font-size: 13px;
}

.template-btn:hover {
  background: #3b82f6;
  color: white;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 8px;
}

.btn-reset,
.btn-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-reset {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-reset:hover {
  background: #e5e7eb;
}

.btn-submit {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
}

.btn-submit:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-submit:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .medical-record-form-container {
    width: 95%;
    margin: 20px;
  }
  
  .form-content {
    padding: 16px;
  }
  
  .patient-info {
    flex-direction: column;
    text-align: center;
  }
  
  .template-buttons {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
