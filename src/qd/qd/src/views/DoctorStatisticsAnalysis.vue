<template>
  <div class="statistics-analysis">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>统计分析</h1>
        <p class="page-description">查看您的工作数据统计和分析报告</p>
      </div>
      <div class="header-controls">
        <!-- 时间范围选择器 -->
        <select v-model="timeRange" @change="handleTimeRangeChange" class="time-range-selector">
          <option value="week">本周</option>
          <option value="month">本月</option>
          <option value="quarter">近三个月</option>
        </select>
        
        <!-- 报告导出按钮 -->
        <button @click="exportReport" class="export-btn" :disabled="exporting">
          <span v-if="exporting">导出中...</span>
          <span v-else>📊 导出报告</span>
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载统计数据...</p>
    </div>

    <!-- 主要内容 -->
    <div v-else class="statistics-content">
      <!-- 第一部分：核心指标概览 -->
      <section class="kpi-section">
        <h2 class="section-title">核心指标概览</h2>
        <div class="kpi-grid">
          <!-- 总服务人次 -->
          <div class="kpi-card">
            <div class="kpi-header">
              <h3>总服务人次</h3>
              <div class="kpi-icon">👥</div>
            </div>
            <div class="kpi-content">
              <div class="kpi-value">{{ kpiData.totalServices || 0 }}</div>
              <div class="kpi-change" :class="getChangeClass(kpiData.totalServicesChange)">
                vs 上一个周期{{ formatChange(kpiData.totalServicesChange) }}
              </div>
            </div>
          </div>

          <!-- 预约完成率 -->
          <div class="kpi-card">
            <div class="kpi-header">
              <h3>预约完成率</h3>
              <div class="kpi-icon">✅</div>
            </div>
            <div class="kpi-content">
              <div class="kpi-value">{{ formatPercentage(kpiData.completionRate) }}</div>
              <div class="kpi-subtitle">
                {{ kpiData.completedAppointments || 0 }} / {{ kpiData.totalAppointments || 0 }} (完成/总数)
              </div>
            </div>
          </div>

          <!-- 新增患者数量 -->
          <div class="kpi-card">
            <div class="kpi-header">
              <h3>新增患者数量</h3>
              <div class="kpi-icon">🆕</div>
            </div>
            <div class="kpi-content">
              <div class="kpi-value">{{ kpiData.newPatients || 0 }}</div>
              <div class="kpi-change" :class="getChangeClass(kpiData.newPatientsChange)">
                vs 上一个周期{{ formatChange(kpiData.newPatientsChange) }}
              </div>
            </div>
          </div>

          <!-- 发布健康指南 -->
          <div class="kpi-card">
            <div class="kpi-header">
              <h3>发布健康指南</h3>
              <div class="kpi-icon">📝</div>
            </div>
            <div class="kpi-content">
              <div class="kpi-value">{{ kpiData.publishedGuides || 0 }}</div>
              <div class="kpi-subtitle">
                {{ getTimeRangeText() }}共发布{{ kpiData.publishedGuides || 0 }}篇
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 第二部分：可视化图表分析 -->
      <section class="charts-section">
        <h2 class="section-title">可视化图表分析</h2>
        <div class="charts-grid">
          <!-- 服务量趋势图 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>服务量趋势</h3>
            </div>
            <div class="chart-container">
              <div ref="serviceTrendChart" class="chart"></div>
            </div>
          </div>

          <!-- 预约状态分配图 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>预约状态分配</h3>
            </div>
            <div class="chart-container">
              <div ref="appointmentStatusChart" class="chart"></div>
            </div>
          </div>

          <!-- 高频服务患者排行 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>高频服务患者 TOP 5</h3>
            </div>
            <div class="chart-container">
              <div ref="topPatientsChart" class="chart"></div>
            </div>
          </div>

          <!-- 预约时间段分析 -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>预约时间段分析</h3>
            </div>
            <div class="chart-container">
              <div ref="scheduleHotnessChart" class="chart"></div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-message">
      <p>{{ error }}</p>
      <button @click="loadAllData" class="retry-btn">重试</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'
import {
  getDoctorKpi,
  getServiceTrend,
  getAppointmentStatus,
  getTopPatients,
  getScheduleHotness,
  exportStatisticsReport
} from '@/api/doctorStatistics'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const error = ref('')
const exporting = ref(false)
const timeRange = ref('month') // 默认本月

// 数据
const kpiData = ref({})

// 图表实例
const serviceTrendChart = ref(null)
const appointmentStatusChart = ref(null)
const topPatientsChart = ref(null)
const scheduleHotnessChart = ref(null)

let serviceTrendChartInstance = null
let appointmentStatusChartInstance = null
let topPatientsChartInstance = null
let scheduleHotnessChartInstance = null

// 方法
const handleTimeRangeChange = () => {
  loadAllData()
}

const getTimeRangeText = () => {
  const textMap = {
    week: '本周',
    month: '本月',
    quarter: '近三个月'
  }
  return textMap[timeRange.value] || '本月'
}

const formatChange = (change) => {
  if (!change) return ''
  return change > 0 ? `+${change}` : `${change}`
}

const getChangeClass = (change) => {
  if (!change) return ''
  return change > 0 ? 'positive' : 'negative'
}

const formatPercentage = (value) => {
  if (value === null || value === undefined) return '0%'
  return `${(value * 100).toFixed(1)}%`
}

// 加载所有数据
const loadAllData = async () => {
  loading.value = true
  error.value = ''

  try {
    const params = { range: timeRange.value }

    // 并行加载所有数据
    const [kpiResponse, trendResponse, statusResponse, patientsResponse, hotnessResponse] = await Promise.all([
      getDoctorKpi(params),
      getServiceTrend(params),
      getAppointmentStatus(params),
      getTopPatients(params),
      getScheduleHotness(params)
    ])

    // 处理KPI数据
    if (kpiResponse.data.code === 200) {
      kpiData.value = kpiResponse.data.data
    }

    // 安全初始化图表
    await safeInitCharts([trendResponse, statusResponse, patientsResponse, hotnessResponse])

  } catch (err) {
    console.error('加载统计数据失败:', err)
    error.value = '加载数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 导出报告
const exportReport = async () => {
  exporting.value = true
  try {
    const params = { range: timeRange.value }
    const response = await exportStatisticsReport(params)

    // 创建下载链接
    const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `医生统计报告_${getTimeRangeText()}_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

  } catch (err) {
    console.error('导出报告失败:', err)
    alert('导出报告失败，请稍后重试')
  } finally {
    exporting.value = false
  }
}

// 初始化服务量趋势图表
const initServiceTrendChart = (data) => {
  if (!serviceTrendChart.value) {
    console.warn('服务趋势图表容器不存在')
    return
  }

  if (serviceTrendChartInstance) {
    serviceTrendChartInstance.dispose()
  }

  serviceTrendChartInstance = echarts.init(serviceTrendChart.value)

  const option = {
    title: {
      text: '服务量趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['预约服务', '在线问诊'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.dates || [],
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '服务次数'
    },
    series: [
      {
        name: '预约服务',
        type: 'line',
        data: data.appointmentCounts || [],
        smooth: true,
        itemStyle: {
          color: '#3b82f6'
        }
      },
      {
        name: '在线问诊',
        type: 'line',
        data: data.consultationCounts || [],
        smooth: true,
        itemStyle: {
          color: '#10b981'
        }
      }
    ]
  }

  serviceTrendChartInstance.setOption(option)
}

// 初始化预约状态分配图表
const initAppointmentStatusChart = (data) => {
  if (!appointmentStatusChart.value) {
    console.warn('预约状态图表容器不存在')
    return
  }

  if (appointmentStatusChartInstance) {
    appointmentStatusChartInstance.dispose()
  }

  appointmentStatusChartInstance = echarts.init(appointmentStatusChart.value)

  const option = {
    title: {
      text: '预约状态分配',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['已完成', '已预约', '已取消']
    },
    series: [
      {
        name: '预约状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          {
            value: data.completed || 0,
            name: '已完成',
            itemStyle: { color: '#10b981' }
          },
          {
            value: data.booked || 0,
            name: '已预约',
            itemStyle: { color: '#3b82f6' }
          },
          {
            value: data.cancelled || 0,
            name: '已取消',
            itemStyle: { color: '#ef4444' }
          }
        ]
      }
    ]
  }

  appointmentStatusChartInstance.setOption(option)
}

// 初始化高频患者排行图表
const initTopPatientsChart = (data) => {
  if (!topPatientsChart.value) {
    console.warn('患者排行图表容器不存在')
    return
  }

  if (topPatientsChartInstance) {
    topPatientsChartInstance.dispose()
  }

  topPatientsChartInstance = echarts.init(topPatientsChart.value)

  const option = {
    title: {
      text: '高频服务患者 TOP 5',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '服务次数'
    },
    yAxis: {
      type: 'category',
      data: (data.patients || []).map(p => p.name),
      axisLabel: {
        interval: 0
      }
    },
    series: [
      {
        name: '服务次数',
        type: 'bar',
        data: (data.patients || []).map(p => p.serviceCount),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#3b82f6' },
            { offset: 1, color: '#1d4ed8' }
          ])
        },
        label: {
          show: true,
          position: 'right',
          formatter: '{c}次'
        }
      }
    ]
  }

  topPatientsChartInstance.setOption(option)
}

// 初始化预约时间段分析图表
const initScheduleHotnessChart = (data) => {
  if (!scheduleHotnessChart.value) {
    console.warn('时间段分析图表容器不存在')
    return
  }

  if (scheduleHotnessChartInstance) {
    scheduleHotnessChartInstance.dispose()
  }

  scheduleHotnessChartInstance = echarts.init(scheduleHotnessChart.value)

  const option = {
    title: {
      text: '预约时间段分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.timeSlots || ['08-10点', '10-12点', '14-16点', '16-18点'],
      axisLabel: {
        interval: 0,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '预约人数'
    },
    series: [
      {
        name: '预约人数',
        type: 'bar',
        data: data.counts || [],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            { offset: 0, color: '#10b981' },
            { offset: 1, color: '#059669' }
          ])
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}人'
        }
      }
    ]
  }

  scheduleHotnessChartInstance.setOption(option)
}

// 安全的图表初始化方法
const safeInitCharts = async (responses) => {
  // 确保DOM完全渲染
  await nextTick()

  // 再次等待确保图表容器有正确的尺寸
  setTimeout(() => {
    const [trendResponse, statusResponse, patientsResponse, hotnessResponse] = responses

    try {
      if (trendResponse.data.code === 200) {
        initServiceTrendChart(trendResponse.data.data)
      }
    } catch (err) {
      console.error('初始化服务趋势图表失败:', err)
    }

    try {
      if (statusResponse.data.code === 200) {
        initAppointmentStatusChart(statusResponse.data.data)
      }
    } catch (err) {
      console.error('初始化预约状态图表失败:', err)
    }

    try {
      if (patientsResponse.data.code === 200) {
        initTopPatientsChart(patientsResponse.data.data)
      }
    } catch (err) {
      console.error('初始化患者排行图表失败:', err)
    }

    try {
      if (hotnessResponse.data.code === 200) {
        initScheduleHotnessChart(hotnessResponse.data.data)
      }
    } catch (err) {
      console.error('初始化时间段分析图表失败:', err)
    }
  }, 200)
}

// 生命周期
onMounted(() => {
  // 检查用户权限
  if (!userStore.userInfo?.isDoctor) {
    router.push('/login')
    return
  }

  // 延迟加载数据，确保组件完全挂载
  setTimeout(() => {
    loadAllData()
  }, 100)

  // 监听窗口大小变化，重新调整图表大小
  window.addEventListener('resize', () => {
    setTimeout(() => {
      if (serviceTrendChartInstance) serviceTrendChartInstance.resize()
      if (appointmentStatusChartInstance) appointmentStatusChartInstance.resize()
      if (topPatientsChartInstance) topPatientsChartInstance.resize()
      if (scheduleHotnessChartInstance) scheduleHotnessChartInstance.resize()
    }, 100)
  })
})

// 组件卸载时清理图表实例
onUnmounted(() => {
  if (serviceTrendChartInstance) {
    serviceTrendChartInstance.dispose()
    serviceTrendChartInstance = null
  }
  if (appointmentStatusChartInstance) {
    appointmentStatusChartInstance.dispose()
    appointmentStatusChartInstance = null
  }
  if (topPatientsChartInstance) {
    topPatientsChartInstance.dispose()
    topPatientsChartInstance = null
  }
  if (scheduleHotnessChartInstance) {
    scheduleHotnessChartInstance.dispose()
    scheduleHotnessChartInstance = null
  }
})
</script>

<style scoped>
.statistics-analysis {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.page-description {
  color: #64748b;
  margin: 0;
  font-size: 16px;
}

.header-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.time-range-selector {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  min-width: 120px;
}

.time-range-selector:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.export-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.export-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误提示 */
.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

.retry-btn {
  margin-top: 12px;
  padding: 8px 16px;
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

/* 内容区域 */
.statistics-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 20px 0;
}

/* KPI指标区域 */
.kpi-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.kpi-card {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.kpi-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.kpi-header h3 {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  margin: 0;
}

.kpi-icon {
  font-size: 24px;
}

.kpi-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.kpi-value {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.kpi-change {
  font-size: 12px;
  font-weight: 500;
}

.kpi-change.positive {
  color: #10b981;
}

.kpi-change.negative {
  color: #ef4444;
}

.kpi-subtitle {
  font-size: 12px;
  color: #64748b;
}

/* 图表区域 */
.charts-section {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 24px;
}

.chart-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.chart-header {
  margin-bottom: 16px;
}

.chart-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.chart-container {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.chart {
  width: 100%;
  height: 300px;
  min-height: 300px;
  min-width: 400px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }

  .chart {
    height: 250px;
  }
}

@media (max-width: 768px) {
  .statistics-analysis {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-controls {
    justify-content: space-between;
  }

  .kpi-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .chart {
    height: 200px;
  }

  .kpi-value {
    font-size: 24px;
  }

  .header-left h1 {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .statistics-analysis {
    padding: 12px;
  }

  .page-header {
    padding: 16px;
  }

  .kpi-section,
  .charts-section {
    padding: 16px;
  }

  .chart {
    height: 180px;
  }
}
</style>
