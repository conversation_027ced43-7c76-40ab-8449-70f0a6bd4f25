<template>
  <div class="schedule-calendar">
    <!-- 日历头部 -->
    <div class="calendar-header">
      <div class="month-navigation">
        <button @click="previousMonth" class="nav-btn">‹</button>
        <h3 class="month-title">{{ currentMonthTitle }}</h3>
        <button @click="nextMonth" class="nav-btn">›</button>
      </div>
      <div class="calendar-actions">
        <button @click="goToToday" class="today-btn">今天</button>
      </div>
    </div>

    <!-- 星期标题 -->
    <div class="weekdays">
      <div v-for="day in weekdays" :key="day" class="weekday">
        {{ day }}
      </div>
    </div>

    <!-- 日历网格 -->
    <div class="calendar-grid">
      <div
        v-for="date in calendarDates"
        :key="date.dateString"
        class="calendar-cell"
        :class="getCellClass(date)"
        @click="handleDateClick(date)"
      >
        <!-- 日期数字 -->
        <div class="date-number">{{ date.day }}</div>
        
        <!-- 排班信息 -->
        <div v-if="date.schedules.length > 0" class="schedules-container">
          <div
            v-for="schedule in date.schedules.slice(0, 3)"
            :key="schedule.id"
            class="schedule-item"
            :class="getScheduleClass(schedule)"
            @click.stop="$emit('edit', schedule)"
          >
            <div class="schedule-time">
              {{ formatTimeDisplay(schedule.startTime) }}
            </div>
            <div class="schedule-slots">
              {{ schedule.availableSlots }}/{{ schedule.totalSlots }}
            </div>
          </div>
          
          <!-- 更多排班指示器 -->
          <div v-if="date.schedules.length > 3" class="more-schedules">
            +{{ date.schedules.length - 3 }}
          </div>
        </div>
        
        <!-- 空状态提示 -->
        <div v-else-if="isCurrentOrFutureDate(date)" class="empty-hint">
          <span class="add-icon">+</span>
        </div>
      </div>
    </div>

    <!-- 日期详情弹窗 -->
    <div v-if="selectedDate" class="date-detail-overlay" @click="closeDetail">
      <div class="date-detail-modal" @click.stop>
        <div class="detail-header">
          <h4>{{ getDateDisplayName(selectedDate.dateString) }}</h4>
          <button @click="closeDetail" class="close-btn">✕</button>
        </div>
        
        <div class="detail-content">
          <div v-if="selectedDate.schedules.length === 0" class="no-schedules">
            <div class="no-schedules-icon">📅</div>
            <p>这一天还没有排班</p>
            <button @click="createScheduleForDate" class="create-schedule-btn">
              创建排班
            </button>
          </div>
          
          <div v-else class="schedules-list">
            <div
              v-for="schedule in selectedDate.schedules"
              :key="schedule.id"
              class="schedule-detail-item"
              :class="getScheduleClass(schedule)"
            >
              <div class="schedule-info">
                <div class="time-info">
                  <span class="time-range">
                    {{ formatTimeDisplay(schedule.startTime) }} - {{ formatTimeDisplay(schedule.endTime) }}
                  </span>
                  <span class="duration">
                    ({{ calculateDuration(schedule.startTime, schedule.endTime) }})
                  </span>
                </div>
                <div class="slots-info">
                  <span class="available">可预约: {{ schedule.availableSlots }}</span>
                  <span class="total">总数: {{ schedule.totalSlots }}</span>
                </div>
                <div class="status-info">
                  <span class="status-badge" :class="getStatusClass(schedule)">
                    {{ getStatusText(schedule) }}
                  </span>
                </div>
              </div>
              
              <div class="schedule-actions">
                <button
                  @click="$emit('edit', schedule)"
                  class="action-btn edit-btn"
                  :disabled="isPastSchedule(schedule)"
                >
                  编辑
                </button>
                <button
                  @click="$emit('delete', schedule)"
                  class="action-btn delete-btn"
                  :disabled="schedule.bookedSlots > 0"
                >
                  删除
                </button>
              </div>
            </div>
            
            <button @click="createScheduleForDate" class="add-more-btn">
              ➕ 添加更多排班
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  formatDate,
  formatTimeDisplay,
  getDateDisplayName,
  isToday,
  isPastDate,
  getWeekday,
  getMonthRange
} from '@/utils/dateUtils'

// Props & Emits
const props = defineProps({
  schedules: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['create', 'edit', 'delete'])

// 响应式数据
const currentDate = ref(new Date())
const selectedDate = ref(null)

const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

// 计算属性
const currentMonthTitle = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth() + 1
  return `${year}年${month}月`
})

const calendarDates = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  
  // 获取当月第一天和最后一天
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  
  // 获取日历开始日期（包含上月末尾几天）
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())
  
  // 获取日历结束日期（包含下月开头几天）
  const endDate = new Date(lastDay)
  endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()))
  
  const dates = []
  const current = new Date(startDate)
  
  while (current <= endDate) {
    const dateString = formatDate(current)
    const daySchedules = props.schedules.filter(s => s.scheduleDate === dateString)
    
    dates.push({
      dateString,
      day: current.getDate(),
      month: current.getMonth(),
      year: current.getFullYear(),
      isCurrentMonth: current.getMonth() === month,
      isToday: isToday(current),
      isPast: isPastDate(current),
      schedules: daySchedules
    })
    
    current.setDate(current.getDate() + 1)
  }
  
  return dates
})

// 方法
const getCellClass = (date) => {
  const classes = []
  
  if (!date.isCurrentMonth) classes.push('other-month')
  if (date.isToday) classes.push('today')
  if (date.isPast) classes.push('past')
  if (date.schedules.length > 0) classes.push('has-schedules')
  
  return classes
}

const getScheduleClass = (schedule) => {
  const classes = ['schedule-item']
  
  if (isPastSchedule(schedule)) {
    classes.push('past')
  } else if (schedule.availableSlots === 0) {
    classes.push('full')
  } else if (schedule.availableSlots <= schedule.totalSlots * 0.2) {
    classes.push('low')
  } else {
    classes.push('available')
  }
  
  return classes
}

const getStatusClass = (schedule) => {
  if (isPastSchedule(schedule)) {
    return 'status-past'
  } else if (schedule.availableSlots === 0) {
    return 'status-full'
  } else if (schedule.availableSlots <= schedule.totalSlots * 0.2) {
    return 'status-low'
  } else {
    return 'status-available'
  }
}

const getStatusText = (schedule) => {
  if (isPastSchedule(schedule)) {
    return '已过期'
  } else if (schedule.availableSlots === 0) {
    return '已满'
  } else if (schedule.availableSlots <= schedule.totalSlots * 0.2) {
    return '紧张'
  } else {
    return '可预约'
  }
}

const isPastSchedule = (schedule) => {
  return isPastDate(schedule.scheduleDate)
}

const isCurrentOrFutureDate = (date) => {
  return !date.isPast && date.isCurrentMonth
}

const calculateDuration = (startTime, endTime) => {
  const start = new Date(`2000-01-01 ${startTime}`)
  const end = new Date(`2000-01-01 ${endTime}`)
  
  if (end <= start) return ''
  
  const diffMs = end.getTime() - start.getTime()
  const hours = Math.floor(diffMs / (1000 * 60 * 60))
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours === 0) {
    return `${minutes}分钟`
  } else if (minutes === 0) {
    return `${hours}小时`
  } else {
    return `${hours}h${minutes}m`
  }
}

const previousMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  currentDate.value = newDate
}

const nextMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  currentDate.value = newDate
}

const goToToday = () => {
  currentDate.value = new Date()
}

const handleDateClick = (date) => {
  if (date.isCurrentMonth) {
    selectedDate.value = date
  }
}

const closeDetail = () => {
  selectedDate.value = null
}

const createScheduleForDate = () => {
  emit('create', selectedDate.value.dateString)
  closeDetail()
}
</script>

<style scoped>
.schedule-calendar {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-bottom: 1px solid #e5e7eb;
}

.month-navigation {
  display: flex;
  align-items: center;
  gap: 16px;
}

.nav-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  color: #3b82f6;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nav-btn:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.month-title {
  margin: 0;
  color: #1e40af;
  font-size: 20px;
  font-weight: 600;
  min-width: 120px;
  text-align: center;
}

.today-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.today-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.weekday {
  padding: 12px;
  text-align: center;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: #e5e7eb;
}

.calendar-cell {
  background: white;
  min-height: 120px;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
  display: flex;
  flex-direction: column;
}

.calendar-cell:hover {
  background: #f8fafc;
}

.calendar-cell.other-month {
  background: #f9fafb;
  color: #9ca3af;
}

.calendar-cell.today {
  background: #fef3c7;
  border: 2px solid #f59e0b;
}

.calendar-cell.past {
  background: #f3f4f6;
  color: #6b7280;
}

.calendar-cell.has-schedules {
  background: #f0f9ff;
}

.date-number {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
  color: inherit;
}

.schedules-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.schedule-item {
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.schedule-item.available {
  background: #dcfce7;
  color: #166534;
}

.schedule-item.low {
  background: #fef3c7;
  color: #92400e;
}

.schedule-item.full {
  background: #fee2e2;
  color: #991b1b;
}

.schedule-item.past {
  background: #f3f4f6;
  color: #6b7280;
}

.schedule-item:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.schedule-time {
  font-weight: 600;
  line-height: 1.2;
}

.schedule-slots {
  font-size: 9px;
  opacity: 0.8;
}

.more-schedules {
  font-size: 10px;
  color: #6b7280;
  text-align: center;
  padding: 2px;
  background: #f3f4f6;
  border-radius: 4px;
  margin-top: 2px;
}

.empty-hint {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.calendar-cell:hover .empty-hint {
  opacity: 0.5;
}

.add-icon {
  font-size: 20px;
  color: #3b82f6;
}

.date-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.date-detail-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12px 12px 0 0;
}

.detail-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.detail-content {
  padding: 24px;
}

.no-schedules {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.no-schedules-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-schedules p {
  margin: 0 0 24px 0;
  font-size: 16px;
}

.create-schedule-btn,
.add-more-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.create-schedule-btn:hover,
.add-more-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.schedules-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.schedule-detail-item {
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.schedule-detail-item.available {
  background: #f0fdf4;
  border-color: #22c55e;
}

.schedule-detail-item.low {
  background: #fffbeb;
  border-color: #f59e0b;
}

.schedule-detail-item.full {
  background: #fef2f2;
  border-color: #ef4444;
}

.schedule-detail-item.past {
  background: #f9fafb;
  border-color: #d1d5db;
  opacity: 0.7;
}

.schedule-info {
  flex: 1;
}

.time-info {
  margin-bottom: 4px;
}

.time-range {
  font-weight: 600;
  color: #1f2937;
  font-size: 16px;
}

.duration {
  color: #6b7280;
  font-size: 14px;
  margin-left: 8px;
}

.slots-info {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
  font-size: 14px;
}

.available {
  color: #059669;
  font-weight: 500;
}

.total {
  color: #374151;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-available {
  background: #dcfce7;
  color: #166534;
}

.status-low {
  background: #fef3c7;
  color: #92400e;
}

.status-full {
  background: #fee2e2;
  color: #991b1b;
}

.status-past {
  background: #f3f4f6;
  color: #6b7280;
}

.schedule-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-btn {
  background: #dbeafe;
  color: #1d4ed8;
}

.edit-btn:hover:not(:disabled) {
  background: #3b82f6;
  color: white;
}

.delete-btn {
  background: #fee2e2;
  color: #dc2626;
}

.delete-btn:hover:not(:disabled) {
  background: #ef4444;
  color: white;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-more-btn {
  width: 100%;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .calendar-cell {
    min-height: 80px;
    padding: 4px;
  }
  
  .date-number {
    font-size: 14px;
  }
  
  .schedule-item {
    font-size: 9px;
  }
  
  .date-detail-modal {
    width: 95%;
    margin: 20px;
  }
  
  .detail-content {
    padding: 16px;
  }
  
  .schedule-detail-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .schedule-actions {
    justify-content: center;
  }
}
</style>
