# API端点映射文档

## 修复后的API端点

### 用户认证API
- ✅ 登录: `POST /api/user/login`
- ✅ 注册: `POST /api/user/register`
- ✅ 登出: `POST /api/user/logout` (已修复)
- ✅ 用户信息: `GET /api/user/profile`

### 医生统计分析API
- ✅ 医生个人统计: `GET /api/doctor/statistics/my`
- ✅ 患者健康数据: `GET /api/doctor/patients/{id}/health-data`
- ✅ 诊疗报告: `GET /api/doctor/reports/treatment`
- ✅ 预约趋势: `GET /api/doctor/statistics/trends` (已修复)
- ✅ 年龄分布: `GET /api/doctor/statistics/age-distribution` (已修复)
- ✅ 疾病分布: `GET /api/doctor/statistics/disease-distribution` (已修复)
- ✅ 工作量统计: `GET /api/doctor/statistics/workload`

### 患者预约API
- ✅ 科室列表: `GET /api/appointments/departments`
- ✅ 医生搜索: `GET /api/appointments/doctors/search`
- ✅ 科室医生: `GET /api/appointments/departments/{id}/doctors`
- ✅ 医生详情: `GET /api/appointments/doctors/{id}`
- ✅ 医生排班: `GET /api/appointments/doctors/{id}/schedules`
- ✅ 创建预约: `POST /api/appointments`
- ✅ 我的预约: `GET /api/appointments/my`
- ✅ 取消预约: `POST /api/appointments/{id}/cancel`
- ✅ 即将到来的预约: `GET /api/appointments/upcoming`

### 医生预约管理API
- ✅ 医生预约列表: `GET /api/doctor/appointments/my`
- ✅ 确认预约: `POST /api/doctor/appointments/{id}/confirm`
- ✅ 完成诊疗: `POST /api/doctor/appointments/{id}/complete`
- ✅ 添加病历: `POST /api/doctor/appointments/{id}/record`

### 健康档案API
- ✅ 档案列表: `GET /api/health/profiles`
- ✅ 档案详情: `GET /api/health/profiles/{id}`
- ✅ 创建档案: `POST /api/health/profiles`
- ✅ 更新档案: `PUT /api/health/profiles/{id}`
- ✅ 删除档案: `DELETE /api/health/profiles/{id}`

## 分页参数标准

### 统一分页格式
```javascript
{
  page: 1,        // 页码，从1开始
  size: 10        // 每页大小
}
```

### 响应格式
```javascript
{
  code: 200,
  message: "success",
  data: {
    content: [...],      // 数据列表
    totalPages: 5,       // 总页数
    totalElements: 50,   // 总记录数
    currentPage: 1,      // 当前页码
    size: 10            // 每页大小
  }
}
```

## 错误处理标准

### HTTP状态码
- `200` - 成功
- `400` - 请求参数错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器错误

### 错误响应格式
```javascript
{
  code: 400,
  message: "具体错误信息",
  data: null
}
```

## 认证机制

### JWT Token
- 请求头: `Authorization: Bearer {token}`
- Token存储: localStorage
- 自动刷新: 401错误时清除token并跳转登录

### 角色权限
- `RESIDENT` - 患者角色
- `DOCTOR` - 医生角色  
- `ADMIN` - 管理员角色

## 测试建议

### 1. 使用API测试页面
访问 `/api-test` 可以：
- 测试所有API接口
- 查看详细请求/响应
- 验证用户状态和权限

### 2. 检查后端服务
```bash
# 检查服务状态
curl http://localhost:8080/api/appointments/departments

# 检查用户认证
curl -H "Authorization: Bearer {token}" http://localhost:8080/api/user/profile
```

### 3. 数据库验证
```sql
-- 检查用户角色
SELECT id, phone_number, role FROM users WHERE role = 'DOCTOR';

-- 检查医生数据
SELECT u.id, u.phone_number, d.real_name, d.status 
FROM users u 
JOIN doctors d ON u.id = d.user_id 
WHERE u.role = 'DOCTOR';

-- 检查科室数据
SELECT * FROM departments;
```

## 常见问题解决

### 1. 404错误
- 检查API端点是否正确
- 确认后端服务运行
- 验证路由配置

### 2. 403权限错误
- 检查用户角色
- 验证JWT Token
- 确认医生用户在doctors表中有记录

### 3. 分页错误
- 确保page参数 >= 1
- 检查size参数合理性
- 验证总页数计算

### 4. 数据为空
- 检查数据库是否有测试数据
- 验证查询条件
- 确认用户权限范围

## 开发环境配置

### Vite代理配置
```javascript
// vite.config.js
export default {
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
}
```

### 环境变量
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8080
VITE_APP_TITLE=社区健康管理系统
```

## 部署注意事项

### 生产环境
- 修改API_BASE_URL为生产地址
- 配置HTTPS
- 设置正确的CORS策略
- 启用API限流和安全防护

### 测试环境
- 使用测试数据库
- 启用详细日志
- 配置错误监控
- 设置自动化测试

---

**最后更新**: 2024年12月
**状态**: 所有主要API端点已修复并测试通过
