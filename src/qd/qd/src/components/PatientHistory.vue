<template>
  <div class="patient-history-overlay">
    <div class="patient-history-container">
      <div class="history-header">
        <h3>📋 患者病历</h3>
        <button @click="$emit('close')" class="close-btn">✕</button>
      </div>

      <div class="history-content">
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner">⏳</div>
          <p>加载中...</p>
        </div>

        <div v-else class="history-data">
          <!-- 患者基本信息 -->
          <div class="patient-summary">
            <div class="patient-info">
              <div class="patient-avatar">
                <span class="avatar-text">
                  {{ getPatientInitial(patient) }}
                </span>
              </div>
              <div class="patient-details">
                <div class="patient-name">{{ getPatientName(patient) }}</div>
                <div class="patient-meta">
                  <span class="age">{{ getPatientAge(patient?.birthDate) }}岁</span>
                  <span class="gender">{{ getPatientGender(patient?.gender) }}</span>
                </div>
                <div class="patient-contact">
                  <span class="phone">📞 {{ patient?.phoneNumber || '未提供' }}</span>
                </div>
                <div v-if="patient?.idCardNumber" class="patient-id">
                  <span class="id-card">🆔 {{ maskIdCard(patient.idCardNumber) }}</span>
                </div>
              </div>
            </div>
            <div class="record-stats">
              <div class="stat-item">
                <div class="stat-number">{{ records.length }}</div>
                <div class="stat-label">诊疗记录</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ uniqueDoctors.length }}</div>
                <div class="stat-label">接诊医生</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ daysSinceFirst }}</div>
                <div class="stat-label">首诊天数</div>
              </div>
            </div>
          </div>

          <!-- 筛选和搜索 -->
          <div class="filters">
            <div class="filter-group">
              <label>时间范围:</label>
              <select v-model="dateFilter" @change="applyFilters" class="filter-select">
                <option value="all">全部时间</option>
                <option value="recent">最近30天</option>
                <option value="3months">最近3个月</option>
                <option value="6months">最近6个月</option>
                <option value="year">最近1年</option>
              </select>
            </div>
            <div class="filter-group">
              <label>医生筛选:</label>
              <select v-model="doctorFilter" @change="applyFilters" class="filter-select">
                <option value="all">全部医生</option>
                <option v-for="doctor in uniqueDoctors" :key="doctor" :value="doctor">
                  {{ doctor }}
                </option>
              </select>
            </div>
            <div class="filter-group">
              <input
                v-model="searchKeyword"
                @input="applyFilters"
                placeholder="搜索诊断、治疗、处方..."
                class="search-input"
              />
            </div>
          </div>

          <!-- 病历记录列表 -->
          <div class="records-section">
            <div v-if="filteredRecords.length === 0" class="empty-state">
              <div class="empty-icon">📋</div>
              <h4>暂无病历记录</h4>
              <p>{{ getEmptyMessage() }}</p>
            </div>

            <div v-else class="records-timeline">
              <div
                v-for="record in filteredRecords"
                :key="record.id"
                class="record-item"
              >
                <div class="record-date-marker">
                  <div class="date-circle"></div>
                  <div class="date-line"></div>
                </div>
                
                <div class="record-content">
                  <div class="record-header">
                    <div class="record-date">
                      {{ formatRecordDate(record.recordDate) }}
                    </div>
                    <div class="record-doctor">
                      👨‍⚕️ {{ record.doctorName || '未知医生' }}
                    </div>
                  </div>
                  
                  <div class="record-body">
                    <div class="record-section">
                      <h5>🔍 诊断结果</h5>
                      <p class="diagnosis-text">{{ record.diagnosis }}</p>
                    </div>
                    
                    <div class="record-section">
                      <h5>💊 治疗方案</h5>
                      <p class="treatment-text">{{ record.treatment }}</p>
                    </div>
                    
                    <div v-if="record.prescription" class="record-section">
                      <h5>📝 处方信息</h5>
                      <p class="prescription-text">{{ record.prescription }}</p>
                    </div>
                  </div>
                  
                  <div class="record-footer">
                    <span class="record-time">
                      记录时间: {{ formatDateTime(record.createdAt) }}
                    </span>
                    <div class="record-actions">
                      <button @click="viewRecordDetail(record)" class="view-detail-btn">
                        查看详情
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 统计摘要 -->
          <div class="summary-section">
            <h4>📊 病历摘要</h4>
            <div class="summary-grid">
              <div class="summary-item">
                <h5>常见诊断</h5>
                <div class="diagnosis-list">
                  <span
                    v-for="diagnosis in topDiagnoses"
                    :key="diagnosis.name"
                    class="diagnosis-tag"
                  >
                    {{ diagnosis.name }} ({{ diagnosis.count }})
                  </span>
                </div>
              </div>
              <div class="summary-item">
                <h5>就诊频率</h5>
                <div class="frequency-info">
                  <p>平均每月就诊 {{ averageVisitsPerMonth }} 次</p>
                  <p>最近一次就诊: {{ lastVisitDate }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getPatientRecords } from '@/api/appointments'
import { getPatientAge, formatAppointmentDate } from '@/utils/appointmentUtils'
import { formatDate } from '@/utils/dateUtils'

// Props & Emits
const props = defineProps({
  patient: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close'])

// 响应式数据
const loading = ref(false)
const records = ref([])
const dateFilter = ref('all')
const doctorFilter = ref('all')
const searchKeyword = ref('')

// 计算属性
const filteredRecords = computed(() => {
  let filtered = [...records.value]

  // 时间筛选
  if (dateFilter.value !== 'all') {
    const now = new Date()
    const filterDate = new Date()
    
    switch (dateFilter.value) {
      case 'recent':
        filterDate.setDate(now.getDate() - 30)
        break
      case '3months':
        filterDate.setMonth(now.getMonth() - 3)
        break
      case '6months':
        filterDate.setMonth(now.getMonth() - 6)
        break
      case 'year':
        filterDate.setFullYear(now.getFullYear() - 1)
        break
    }
    
    filtered = filtered.filter(record => 
      new Date(record.recordDate) >= filterDate
    )
  }

  // 医生筛选
  if (doctorFilter.value !== 'all') {
    filtered = filtered.filter(record => 
      record.doctorName === doctorFilter.value
    )
  }

  // 关键词搜索
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.trim().toLowerCase()
    filtered = filtered.filter(record =>
      record.diagnosis.toLowerCase().includes(keyword) ||
      record.treatment.toLowerCase().includes(keyword) ||
      (record.prescription && record.prescription.toLowerCase().includes(keyword))
    )
  }

  // 按日期倒序排列
  return filtered.sort((a, b) => new Date(b.recordDate) - new Date(a.recordDate))
})

const uniqueDoctors = computed(() => {
  const doctors = [...new Set(records.value.map(r => r.doctorName).filter(Boolean))]
  return doctors.sort()
})

const daysSinceFirst = computed(() => {
  if (records.value.length === 0) return 0
  
  const firstRecord = records.value.reduce((earliest, record) => {
    return new Date(record.recordDate) < new Date(earliest.recordDate) ? record : earliest
  })
  
  const daysDiff = Math.floor((new Date() - new Date(firstRecord.recordDate)) / (1000 * 60 * 60 * 24))
  return daysDiff
})

const topDiagnoses = computed(() => {
  const diagnosisCount = {}
  
  records.value.forEach(record => {
    const diagnosis = record.diagnosis.trim()
    diagnosisCount[diagnosis] = (diagnosisCount[diagnosis] || 0) + 1
  })
  
  return Object.entries(diagnosisCount)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)
})

const averageVisitsPerMonth = computed(() => {
  if (records.value.length === 0 || daysSinceFirst.value === 0) return 0
  
  const months = daysSinceFirst.value / 30
  return Math.round((records.value.length / months) * 10) / 10
})

const lastVisitDate = computed(() => {
  if (records.value.length === 0) return '无记录'
  
  const lastRecord = records.value.reduce((latest, record) => {
    return new Date(record.recordDate) > new Date(latest.recordDate) ? record : latest
  })
  
  return formatRecordDate(lastRecord.recordDate)
})

// 方法
const getPatientName = (patient) => {
  return patient?.name || patient?.profileOwnerName || '未知患者'
}

const getPatientInitial = (patient) => {
  const name = getPatientName(patient)
  return name.charAt(0).toUpperCase()
}

const getPatientGender = (gender) => {
  const genderMap = {
    'male': '男',
    'female': '女',
    'MALE': '男',
    'FEMALE': '女',
    'OTHER': '其他'
  }
  return genderMap[gender] || ''
}

const maskIdCard = (idCard) => {
  if (!idCard) return ''
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
}

const formatRecordDate = (date) => {
  if (!date) return ''
  
  const d = new Date(date)
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  
  return `${year}年${month}月${day}日`
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

const getEmptyMessage = () => {
  if (dateFilter.value !== 'all' || doctorFilter.value !== 'all' || searchKeyword.value.trim()) {
    return '没有找到符合条件的病历记录，请调整筛选条件'
  }
  return '该患者暂无病历记录'
}

const applyFilters = () => {
  // 筛选逻辑在计算属性中处理
}

const viewRecordDetail = (record) => {
  // 可以实现查看详情功能
  alert(`病历详情:\n\n诊断: ${record.diagnosis}\n\n治疗: ${record.treatment}\n\n处方: ${record.prescription || '无'}`)
}

const loadPatientRecords = async () => {
  loading.value = true
  try {
    const response = await getPatientRecords(props.patient.id)
    if (response.data.code === 200) {
      records.value = response.data.data || []
    } else {
      console.error('获取患者病历失败:', response.data.message)
      records.value = []
    }
  } catch (error) {
    console.error('获取患者病历失败:', error)
    records.value = []
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadPatientRecords()
})
</script>

<style scoped>
.patient-history-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.patient-history-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12px 12px 0 0;
}

.history-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.history-content {
  padding: 24px;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loading-spinner {
  font-size: 48px;
  margin-bottom: 16px;
}

.patient-summary {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.patient-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 600;
  flex-shrink: 0;
}

.patient-name {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.patient-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #6b7280;
}

.patient-contact,
.patient-id {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.record-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.filters {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  white-space: nowrap;
}

.filter-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  min-width: 120px;
}

.search-input {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  width: 200px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 18px;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

.records-timeline {
  position: relative;
}

.record-item {
  display: flex;
  margin-bottom: 24px;
  position: relative;
}

.record-date-marker {
  position: relative;
  margin-right: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.date-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #3b82f6;
  border: 3px solid white;
  box-shadow: 0 0 0 2px #3b82f6;
  z-index: 1;
}

.date-line {
  width: 2px;
  height: 100%;
  background: #e5e7eb;
  position: absolute;
  top: 18px;
  left: 50%;
  transform: translateX(-50%);
}

.record-item:last-child .date-line {
  display: none;
}

.record-content {
  flex: 1;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.record-date {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.record-doctor {
  font-size: 14px;
  color: #3b82f6;
  font-weight: 500;
}

.record-body {
  margin-bottom: 16px;
}

.record-section {
  margin-bottom: 16px;
}

.record-section:last-child {
  margin-bottom: 0;
}

.record-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.diagnosis-text,
.treatment-text,
.prescription-text {
  margin: 0;
  font-size: 14px;
  color: #1f2937;
  line-height: 1.6;
  background: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
}

.record-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f3f4f6;
}

.record-time {
  font-size: 12px;
  color: #6b7280;
}

.view-detail-btn {
  padding: 6px 12px;
  background: #f0f9ff;
  color: #0369a1;
  border: 1px solid #0369a1;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.view-detail-btn:hover {
  background: #0369a1;
  color: white;
}

.summary-section {
  margin-top: 32px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.summary-section h4 {
  margin: 0 0 16px 0;
  color: #1e40af;
  font-size: 16px;
  font-weight: 600;
}

.summary-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.summary-item h5 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 14px;
  font-weight: 600;
}

.diagnosis-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.diagnosis-tag {
  padding: 4px 8px;
  background: #dbeafe;
  color: #1d4ed8;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.frequency-info p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .patient-history-container {
    width: 95%;
    margin: 20px;
  }
  
  .history-content {
    padding: 16px;
  }
  
  .patient-summary {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .patient-info {
    justify-content: center;
  }
  
  .record-stats {
    justify-content: space-around;
  }
  
  .filters {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .filter-group {
    justify-content: space-between;
  }
  
  .search-input {
    width: 100%;
  }
  
  .record-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
}
</style>
