<template>
  <div class="api-test-page">
    <div class="page-header">
      <h1>API连接测试</h1>
      <p>测试前端与后端API的连接状态</p>
    </div>

    <div class="test-sections">
      <!-- 用户信息 -->
      <div class="test-section">
        <h2>当前用户信息</h2>
        <div class="user-info">
          <div class="info-item">
            <label>登录状态:</label>
            <span :class="userStore.isLoggedIn ? 'status-success' : 'status-error'">
              {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
            </span>
          </div>
          <div class="info-item">
            <label>用户ID:</label>
            <span>{{ userStore.userInfo?.id || '未知' }}</span>
          </div>
          <div class="info-item">
            <label>用户角色:</label>
            <span>{{ userStore.userInfo?.role || '未知' }}</span>
          </div>
          <div class="info-item">
            <label>昵称:</label>
            <span>{{ userStore.userInfo?.nickname || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label>手机号:</label>
            <span>{{ userStore.userInfo?.phoneNumber || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label>Token:</label>
            <span class="token-display">{{ userStore.token ? '已设置' : '未设置' }}</span>
          </div>
        </div>
      </div>

      <!-- API测试 -->
      <div class="test-section">
        <h2>API接口测试</h2>
        
        <!-- 医生统计API测试 -->
        <div class="api-group" v-if="userStore.userInfo?.role === 'DOCTOR'">
          <h3>医生统计分析API</h3>
          <div class="api-buttons">
            <button @click="testDoctorStats" :disabled="loading" class="test-btn">
              测试医生统计
            </button>
            <button @click="testPatientHealthData" :disabled="loading" class="test-btn">
              测试患者健康数据
            </button>
            <button @click="testTreatmentReport" :disabled="loading" class="test-btn">
              测试诊疗报告
            </button>
          </div>
        </div>

        <!-- 患者预约API测试 -->
        <div class="api-group">
          <h3>患者预约API</h3>
          <div class="api-buttons">
            <button @click="testDepartments" :disabled="loading" class="test-btn">
              测试科室列表
            </button>
            <button @click="testSearchDoctors" :disabled="loading" class="test-btn">
              测试医生搜索
            </button>
            <button @click="testMyAppointments" :disabled="loading" class="test-btn">
              测试我的预约
            </button>
            <button @click="testUpcomingAppointments" :disabled="loading" class="test-btn">
              测试即将到来的预约
            </button>
          </div>
        </div>

        <!-- 健康档案API测试 -->
        <div class="api-group">
          <h3>健康档案API</h3>
          <div class="api-buttons">
            <button @click="testHealthProfiles" :disabled="loading" class="test-btn">
              测试健康档案
            </button>
          </div>
        </div>
      </div>

      <!-- 测试结果 -->
      <div class="test-section" v-if="testResults.length > 0">
        <h2>测试结果</h2>
        <div class="test-results">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="test-result"
            :class="result.success ? 'success' : 'error'"
          >
            <div class="result-header">
              <span class="result-status">
                {{ result.success ? '✅' : '❌' }}
              </span>
              <span class="result-title">{{ result.title }}</span>
              <span class="result-time">{{ formatTime(result.timestamp) }}</span>
            </div>
            
            <div class="result-details">
              <div class="result-message">{{ result.message }}</div>
              
              <div v-if="result.request" class="result-request">
                <h4>请求信息:</h4>
                <pre>{{ JSON.stringify(result.request, null, 2) }}</pre>
              </div>
              
              <div v-if="result.response" class="result-response">
                <h4>响应数据:</h4>
                <pre>{{ JSON.stringify(result.response, null, 2) }}</pre>
              </div>
              
              <div v-if="result.error" class="result-error">
                <h4>错误信息:</h4>
                <pre>{{ JSON.stringify(result.error, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速导航 -->
      <div class="test-section">
        <h2>快速导航</h2>
        <div class="nav-buttons">
          <router-link to="/doctor/statistics" class="nav-btn doctor-btn" v-if="userStore.userInfo?.role === 'DOCTOR'">
            📊 医生统计分析
          </router-link>
          <router-link to="/appointments" class="nav-btn patient-btn">
            📋 我的预约
          </router-link>
          <router-link to="/booking" class="nav-btn patient-btn">
            📅 预约挂号
          </router-link>
          <router-link to="/" class="nav-btn home-btn">
            🏠 返回首页
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useUserStore } from '@/stores/user'
import * as doctorStatsApi from '@/api/doctorStats'
import * as appointmentsApi from '@/api/appointments'
import * as healthApi from '@/api/health'

export default {
  name: 'ApiTestPage',
  setup() {
    const userStore = useUserStore()
    const loading = ref(false)
    const testResults = ref([])

    // 添加测试结果
    const addTestResult = (title, success, message, request = null, response = null, error = null) => {
      testResults.value.unshift({
        title,
        success,
        message,
        request,
        response,
        error,
        timestamp: new Date()
      })
      
      // 只保留最近20条结果
      if (testResults.value.length > 20) {
        testResults.value = testResults.value.slice(0, 20)
      }
    }

    // 测试医生统计
    const testDoctorStats = async () => {
      loading.value = true
      try {
        const params = {
          period: 'month',
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        }
        
        const response = await doctorStatsApi.getDoctorStatistics(params)
        
        addTestResult(
          '医生统计API',
          true,
          `成功获取医生统计数据，状态码: ${response.status}`,
          { url: '/api/doctor/statistics/my', params },
          response.data
        )
      } catch (error) {
        addTestResult(
          '医生统计API',
          false,
          `请求失败: ${error.message}`,
          { url: '/api/doctor/statistics/my' },
          null,
          {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
          }
        )
      } finally {
        loading.value = false
      }
    }

    // 测试患者健康数据
    const testPatientHealthData = async () => {
      loading.value = true
      try {
        const patientId = 1
        const params = {
          metricType: 'blood_pressure',
          startDate: '2024-01-01',
          endDate: '2024-12-31'
        }
        
        const response = await doctorStatsApi.getPatientHealthData(patientId, params)
        
        addTestResult(
          '患者健康数据API',
          true,
          `成功获取患者健康数据，状态码: ${response.status}`,
          { url: `/api/doctor/patients/${patientId}/health-data`, params },
          response.data
        )
      } catch (error) {
        addTestResult(
          '患者健康数据API',
          false,
          `请求失败: ${error.message}`,
          { url: '/api/doctor/patients/1/health-data' },
          null,
          {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
          }
        )
      } finally {
        loading.value = false
      }
    }

    // 测试诊疗报告
    const testTreatmentReport = async () => {
      loading.value = true
      try {
        const params = {
          startDate: '2024-01-01',
          endDate: '2024-12-31',
          reportType: 'summary',
          format: 'json'
        }
        
        const response = await doctorStatsApi.generateTreatmentReport(params)
        
        addTestResult(
          '诊疗报告API',
          true,
          `成功生成诊疗报告，状态码: ${response.status}`,
          { url: '/api/doctor/reports/treatment', params },
          response.data
        )
      } catch (error) {
        addTestResult(
          '诊疗报告API',
          false,
          `请求失败: ${error.message}`,
          { url: '/api/doctor/reports/treatment' },
          null,
          {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
          }
        )
      } finally {
        loading.value = false
      }
    }

    // 测试科室列表
    const testDepartments = async () => {
      loading.value = true
      try {
        const response = await appointmentsApi.getDepartments()
        
        addTestResult(
          '科室列表API',
          true,
          `成功获取科室列表，状态码: ${response.status}`,
          { url: '/api/appointments/departments' },
          response.data
        )
      } catch (error) {
        addTestResult(
          '科室列表API',
          false,
          `请求失败: ${error.message}`,
          { url: '/api/appointments/departments' },
          null,
          {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
          }
        )
      } finally {
        loading.value = false
      }
    }

    // 测试医生搜索
    const testSearchDoctors = async () => {
      loading.value = true
      try {
        const params = {
          name: '王',
          page: 1,
          size: 10
        }
        
        const response = await appointmentsApi.searchDoctors(params)
        
        addTestResult(
          '医生搜索API',
          true,
          `成功搜索医生，状态码: ${response.status}`,
          { url: '/api/appointments/doctors/search', params },
          response.data
        )
      } catch (error) {
        addTestResult(
          '医生搜索API',
          false,
          `请求失败: ${error.message}`,
          { url: '/api/appointments/doctors/search' },
          null,
          {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
          }
        )
      } finally {
        loading.value = false
      }
    }

    // 测试我的预约
    const testMyAppointments = async () => {
      loading.value = true
      try {
        const params = {
          page: 1,
          size: 10
        }
        
        const response = await appointmentsApi.getMyAppointments(params)
        
        addTestResult(
          '我的预约API',
          true,
          `成功获取我的预约，状态码: ${response.status}`,
          { url: '/api/appointments/my', params },
          response.data
        )
      } catch (error) {
        addTestResult(
          '我的预约API',
          false,
          `请求失败: ${error.message}`,
          { url: '/api/appointments/my' },
          null,
          {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
          }
        )
      } finally {
        loading.value = false
      }
    }

    // 测试即将到来的预约
    const testUpcomingAppointments = async () => {
      loading.value = true
      try {
        const params = { days: 7 }
        
        const response = await appointmentsApi.getUpcomingAppointments(params)
        
        addTestResult(
          '即将到来的预约API',
          true,
          `成功获取即将到来的预约，状态码: ${response.status}`,
          { url: '/api/appointments/upcoming', params },
          response.data
        )
      } catch (error) {
        addTestResult(
          '即将到来的预约API',
          false,
          `请求失败: ${error.message}`,
          { url: '/api/appointments/upcoming' },
          null,
          {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
          }
        )
      } finally {
        loading.value = false
      }
    }

    // 测试健康档案
    const testHealthProfiles = async () => {
      loading.value = true
      try {
        const response = await healthApi.getHealthProfiles()
        
        addTestResult(
          '健康档案API',
          true,
          `成功获取健康档案，状态码: ${response.status}`,
          { url: '/api/health/profiles' },
          response.data
        )
      } catch (error) {
        addTestResult(
          '健康档案API',
          false,
          `请求失败: ${error.message}`,
          { url: '/api/health/profiles' },
          null,
          {
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
          }
        )
      } finally {
        loading.value = false
      }
    }

    // 格式化时间
    const formatTime = (timestamp) => {
      return timestamp.toLocaleTimeString('zh-CN')
    }

    return {
      userStore,
      loading,
      testResults,
      testDoctorStats,
      testPatientHealthData,
      testTreatmentReport,
      testDepartments,
      testSearchDoctors,
      testMyAppointments,
      testUpcomingAppointments,
      testHealthProfiles,
      formatTime
    }
  }
}
</script>

<style scoped>
.api-test-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 32px;
}

.page-header p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.test-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.test-section h2 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 24px;
  border-bottom: 2px solid #4A90E2;
  padding-bottom: 10px;
}

.user-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.info-item label {
  font-weight: 500;
  color: #495057;
}

.status-success {
  color: #28a745;
  font-weight: 500;
}

.status-error {
  color: #dc3545;
  font-weight: 500;
}

.token-display {
  font-family: monospace;
  font-size: 12px;
}

.api-group {
  margin-bottom: 25px;
}

.api-group h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 18px;
}

.api-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 10px 20px;
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.test-btn:hover:not(:disabled) {
  background: #138496;
  transform: translateY(-1px);
}

.test-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.test-result {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.test-result.success {
  border-left: 4px solid #28a745;
}

.test-result.error {
  border-left: 4px solid #dc3545;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.result-status {
  font-size: 18px;
}

.result-title {
  font-weight: 500;
  color: #2c3e50;
  flex: 1;
}

.result-time {
  color: #6c757d;
  font-size: 12px;
}

.result-details {
  padding: 15px;
}

.result-message {
  margin-bottom: 15px;
  color: #495057;
}

.result-request,
.result-response,
.result-error {
  margin-bottom: 15px;
}

.result-request h4,
.result-response h4,
.result-error h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 14px;
}

.result-request pre,
.result-response pre,
.result-error pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  font-size: 12px;
  overflow-x: auto;
  margin: 0;
}

.nav-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.nav-btn {
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.doctor-btn {
  background: linear-gradient(135deg, #4A90E2, #357abd);
  color: white;
}

.doctor-btn:hover {
  background: linear-gradient(135deg, #357abd, #2c5aa0);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.patient-btn {
  background: linear-gradient(135deg, #28a745, #218838);
  color: white;
}

.patient-btn:hover {
  background: linear-gradient(135deg, #218838, #1e7e34);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.home-btn {
  background: linear-gradient(135deg, #6c757d, #5a6268);
  color: white;
}

.home-btn:hover {
  background: linear-gradient(135deg, #5a6268, #495057);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

@media (max-width: 768px) {
  .user-info {
    grid-template-columns: 1fr;
  }
  
  .api-buttons {
    flex-direction: column;
  }
  
  .nav-buttons {
    flex-direction: column;
  }
  
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>
