import api from './index'

/**
 * 获取医生个人工作统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期 (YYYY-MM-DD)
 * @param {string} params.endDate - 结束日期 (YYYY-MM-DD)
 * @param {string} params.period - 统计周期 (day/week/month/year)
 * @returns {Promise} 工作统计响应
 */
export const getDoctorStatistics = (params = {}) => {
  return api.get('/doctor/statistics/my', { params })
}

/**
 * 获取患者健康数据
 * @param {number} patientId - 患者ID
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {string} params.metricType - 指标类型 (blood_pressure/blood_sugar/weight/heart_rate/temperature)
 * @returns {Promise} 患者健康数据响应
 */
export const getPatientHealthData = (patientId, params = {}) => {
  return api.get(`/doctor/patients/${patientId}/health-data`, { params })
}

/**
 * 生成诊疗报告
 * @param {Object} params - 报告参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {string} params.reportType - 报告类型 (summary/detailed/statistical)
 * @param {string} params.format - 导出格式 (json/pdf/excel)
 * @returns {Promise} 诊疗报告响应
 */
export const generateTreatmentReport = (params = {}) => {
  return api.get('/doctor/reports/treatment', { 
    params,
    responseType: params.format === 'json' ? 'json' : 'blob'
  })
}

/**
 * 获取医生工作量统计
 * @param {Object} params - 查询参数
 * @param {string} params.period - 统计周期 (today/week/month/quarter/year)
 * @param {string} params.startDate - 自定义开始日期
 * @param {string} params.endDate - 自定义结束日期
 * @returns {Promise} 工作量统计响应
 */
export const getDoctorWorkload = (params = {}) => {
  return api.get('/doctor/statistics/my', { params: { ...params, type: 'workload' } })
}

/**
 * 获取患者满意度统计
 * @param {Object} params - 查询参数
 * @param {string} params.period - 统计周期
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @returns {Promise} 患者满意度统计响应
 */
export const getPatientSatisfaction = (params = {}) => {
  return api.get('/doctor/statistics/satisfaction', { params })
}

/**
 * 获取疾病分布统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {string} params.groupBy - 分组方式 (disease/age/gender)
 * @returns {Promise} 疾病分布统计响应
 */
export const getDiseaseDistribution = (params = {}) => {
  return api.get('/doctor/statistics/my', { params: { ...params, type: 'disease-distribution' } })
}

/**
 * 获取收入统计
 * @param {Object} params - 查询参数
 * @param {string} params.period - 统计周期
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise} 收入统计响应
 */
export const getIncomeStatistics = (params = {}) => {
  return api.get('/doctor/statistics/income', { params })
}

/**
 * 获取预约趋势分析
 * @param {Object} params - 查询参数
 * @param {string} params.period - 分析周期 (7days/30days/90days/1year)
 * @param {string} params.granularity - 数据粒度 (hour/day/week/month)
 * @returns {Promise} 预约趋势分析响应
 */
export const getAppointmentTrends = (params = {}) => {
  return api.get('/doctor/statistics/my', { params: { ...params, type: 'trends' } })
}

/**
 * 获取患者年龄分布
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise} 患者年龄分布响应
 */
export const getPatientAgeDistribution = (params = {}) => {
  return api.get('/doctor/statistics/my', { params: { ...params, type: 'age-distribution' } })
}

/**
 * 获取治疗效果统计
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {string} params.diseaseType - 疾病类型
 * @returns {Promise} 治疗效果统计响应
 */
export const getTreatmentEffectiveness = (params = {}) => {
  return api.get('/doctor/statistics/treatment-effectiveness', { params })
}

/**
 * 获取医生排名统计
 * @param {Object} params - 查询参数
 * @param {string} params.rankBy - 排名依据 (appointments/satisfaction/income)
 * @param {string} params.period - 统计周期
 * @param {number} params.limit - 返回数量限制
 * @returns {Promise} 医生排名统计响应
 */
export const getDoctorRanking = (params = {}) => {
  return api.get('/doctor/statistics/ranking', { params })
}

/**
 * 导出统计报告
 * @param {Object} params - 导出参数
 * @param {string} params.reportType - 报告类型
 * @param {string} params.format - 导出格式 (excel/pdf/csv)
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise} 导出响应
 */
export const exportStatisticsReport = (params = {}) => {
  return api.get('/doctor/statistics/export', {
    params,
    responseType: 'blob'
  })
}

/**
 * 获取实时统计数据
 * @returns {Promise} 实时统计数据响应
 */
export const getRealTimeStats = () => {
  return api.get('/doctor/statistics/realtime')
}

/**
 * 获取对比统计数据
 * @param {Object} params - 对比参数
 * @param {string} params.currentPeriod - 当前周期
 * @param {string} params.comparePeriod - 对比周期
 * @param {Array} params.metrics - 对比指标数组
 * @returns {Promise} 对比统计数据响应
 */
export const getComparisonStats = (params = {}) => {
  return api.get('/doctor/statistics/comparison', { params })
}

/**
 * 获取患者健康指标趋势
 * @param {number} patientId - 患者ID
 * @param {Object} params - 查询参数
 * @param {string} params.metricType - 指标类型
 * @param {string} params.period - 时间周期
 * @param {number} params.limit - 数据点数量限制
 * @returns {Promise} 健康指标趋势响应
 */
export const getPatientHealthTrends = (patientId, params = {}) => {
  return api.get(`/doctor/patients/${patientId}/health-trends`, { params })
}

/**
 * 获取患者诊疗历史统计
 * @param {number} patientId - 患者ID
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise} 诊疗历史统计响应
 */
export const getPatientTreatmentHistory = (patientId, params = {}) => {
  return api.get(`/doctor/patients/${patientId}/treatment-history`, { params })
}

/**
 * 获取科室统计对比
 * @param {Object} params - 查询参数
 * @param {string} params.period - 统计周期
 * @param {Array} params.metrics - 对比指标
 * @returns {Promise} 科室统计对比响应
 */
export const getDepartmentComparison = (params = {}) => {
  return api.get('/doctor/statistics/department-comparison', { params })
}

/**
 * 获取预约取消率统计
 * @param {Object} params - 查询参数
 * @param {string} params.period - 统计周期
 * @param {string} params.groupBy - 分组方式 (day/week/month)
 * @returns {Promise} 预约取消率统计响应
 */
export const getAppointmentCancellationRate = (params = {}) => {
  return api.get('/doctor/statistics/cancellation-rate', { params })
}

/**
 * 获取医生绩效评估
 * @param {Object} params - 查询参数
 * @param {string} params.evaluationPeriod - 评估周期
 * @param {Array} params.kpiMetrics - KPI指标数组
 * @returns {Promise} 绩效评估响应
 */
export const getDoctorPerformanceEvaluation = (params = {}) => {
  return api.get('/doctor/statistics/performance', { params })
}
