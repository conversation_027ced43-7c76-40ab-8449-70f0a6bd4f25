<template>
  <div class="user-profile-form">
    <div class="form-header">
      <h3>个人信息管理</h3>
      <button @click="$emit('close')" class="close-btn">✕</button>
    </div>

    <div class="form-content">
      <!-- 头像区域 -->
      <div class="avatar-section">
        <div class="avatar-container">
          <div class="user-avatar" :style="{ backgroundImage: form.avatarUrl ? `url(${form.avatarUrl})` : 'none' }">
            <span v-if="!form.avatarUrl" class="avatar-placeholder">
              {{ form.nickname?.charAt(0) || userInfo?.nickname?.charAt(0) || 'U' }}
            </span>
          </div>
          <button @click="showAvatarInput = !showAvatarInput" class="change-avatar-btn">
            📷 更换头像
          </button>
        </div>
        
        <!-- 头像URL输入 -->
        <div v-if="showAvatarInput" class="avatar-input-section">
          <label for="avatarUrl">头像链接</label>
          <input
            id="avatarUrl"
            v-model="form.avatarUrl"
            type="url"
            placeholder="请输入头像图片链接"
            class="form-input"
            maxlength="500"
          />
          <span class="help-text">支持 jpg、png、gif 格式图片</span>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="profile-form">
        <!-- 基础信息 -->
        <div class="form-section">
          <h4>基础信息</h4>
          
          <!-- 手机号 -->
          <div class="form-group">
            <label for="phoneNumber">手机号 *</label>
            <input
              id="phoneNumber"
              v-model="form.phoneNumber"
              type="tel"
              placeholder="请输入11位手机号"
              required
              pattern="1[3-9]\d{9}"
              maxlength="11"
              class="form-input"
            />
            <span class="help-text">请输入有效的11位手机号码</span>
          </div>

          <!-- 昵称 -->
          <div class="form-group">
            <label for="nickname">昵称 *</label>
            <input
              id="nickname"
              v-model="form.nickname"
              type="text"
              placeholder="请输入昵称"
              required
              maxlength="50"
              class="form-input"
            />
            <span class="help-text">{{ form.nickname?.length || 0 }}/50</span>
          </div>

          <!-- 医生真实姓名 (仅医生可见) -->
          <div v-if="userInfo?.isDoctor" class="form-group">
            <label for="realName">真实姓名</label>
            <input
              id="realName"
              v-model="form.realName"
              type="text"
              placeholder="请输入真实姓名"
              maxlength="50"
              class="form-input"
            />
            <span class="help-text">医生专用字段</span>
          </div>
        </div>

        <!-- 角色信息 (只读) -->
        <div class="form-section">
          <h4>角色信息</h4>
          
          <div class="role-info">
            <div class="role-item">
              <span class="role-label">用户类型:</span>
              <span class="role-value">{{ getRoleText() }}</span>
            </div>
            
            <div v-if="userInfo?.isDoctor" class="role-item">
              <span class="role-label">科室:</span>
              <span class="role-value">{{ userInfo.departmentName || '未设置' }}</span>
            </div>
            
            <div v-if="userInfo?.isDoctor" class="role-item">
              <span class="role-label">职称:</span>
              <span class="role-value">{{ userInfo.title || '未设置' }}</span>
            </div>
            
            <div v-if="userInfo?.isDoctor" class="role-item">
              <span class="role-label">审核状态:</span>
              <span class="role-value" :class="getStatusClass()">
                {{ getStatusText() }}
              </span>
            </div>
          </div>
        </div>

        <!-- 表单按钮 -->
        <div class="form-actions">
          <button type="button" @click="resetForm" class="btn-reset">
            重置
          </button>
          <button type="submit" :disabled="!isFormValid || loading" class="btn-submit">
            <span v-if="loading">保存中...</span>
            <span v-else>保存更改</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { getUserInfo, updateUserProfile } from '@/api/user'

// Props & Emits
const emit = defineEmits(['close', 'success'])

// 响应式数据
const userInfo = ref(null)
const loading = ref(false)
const showAvatarInput = ref(false)

const form = ref({
  nickname: '',
  phoneNumber: '',
  avatarUrl: '',
  realName: ''
})

// 原始数据备份
const originalData = ref({})

// 计算属性
const isFormValid = computed(() => {
  const phonePattern = /^1[3-9]\d{9}$/
  return form.value.nickname &&
         form.value.nickname.trim().length > 0 &&
         form.value.phoneNumber &&
         phonePattern.test(form.value.phoneNumber)
})

const hasChanges = computed(() => {
  return JSON.stringify(form.value) !== JSON.stringify(originalData.value)
})

// 方法
const getRoleText = () => {
  if (userInfo.value?.isAdmin) return '管理员'
  if (userInfo.value?.isDoctor) return '医生'
  return '居民用户'
}

const getStatusText = () => {
  if (!userInfo.value?.isDoctor) return ''
  switch (userInfo.value.doctorStatus) {
    case 'PENDING': return '待审核'
    case 'APPROVED': return '已通过'
    case 'REJECTED': return '已拒绝'
    default: return '未知状态'
  }
}

const getStatusClass = () => {
  if (!userInfo.value?.isDoctor) return ''
  switch (userInfo.value.doctorStatus) {
    case 'PENDING': return 'status-pending'
    case 'APPROVED': return 'status-approved'
    case 'REJECTED': return 'status-rejected'
    default: return ''
  }
}

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const response = await getUserInfo()
    if (response.data.code === 200) {
      userInfo.value = response.data.data
      
      // 初始化表单数据
      form.value = {
        nickname: userInfo.value.nickname || '',
        phoneNumber: userInfo.value.phoneNumber || '',
        avatarUrl: userInfo.value.avatarUrl || '',
        realName: userInfo.value.realName || ''
      }
      
      // 备份原始数据
      originalData.value = { ...form.value }
    } else {
      console.error('获取用户信息失败:', response.data.message)
      alert('获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    alert('获取用户信息失败，请重试')
  }
}

// 重置表单
const resetForm = () => {
  form.value = { ...originalData.value }
  showAvatarInput.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!isFormValid.value || !hasChanges.value) return

  loading.value = true
  try {
    // 准备提交数据，只包含有变化的字段
    const submitData = {}
    
    if (form.value.nickname !== originalData.value.nickname) {
      submitData.nickname = form.value.nickname
    }

    if (form.value.phoneNumber !== originalData.value.phoneNumber) {
      submitData.phoneNumber = form.value.phoneNumber
    }

    if (form.value.avatarUrl !== originalData.value.avatarUrl) {
      submitData.avatarUrl = form.value.avatarUrl
    }

    if (userInfo.value?.isDoctor && form.value.realName !== originalData.value.realName) {
      submitData.realName = form.value.realName
    }

    const response = await updateUserProfile(submitData)
    
    if (response.data.code === 200) {
      // 更新原始数据
      originalData.value = { ...form.value }
      
      emit('success', '个人信息更新成功！')
    } else {
      alert('更新失败：' + response.data.message)
    }
  } catch (error) {
    console.error('更新个人信息失败:', error)
    alert('更新失败，请重试')
  } finally {
    loading.value = false
  }
}

// 监听头像URL变化，自动显示预览
watch(() => form.value.avatarUrl, (newUrl) => {
  if (newUrl && newUrl.trim()) {
    // 可以在这里添加图片预加载逻辑
  }
})

// 生命周期
onMounted(() => {
  loadUserInfo()
})
</script>

<style scoped>
.user-profile-form {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

/* 表单头部 */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.form-header h3 {
  color: #262626;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  color: #8c8c8c;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #ff4d4f;
  color: white;
}

/* 表单内容 */
.form-content {
  padding: 28px;
  overflow-y: auto;
  flex: 1;
}

/* 头像区域 */
.avatar-section {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 600;
  background-size: cover;
  background-position: center;
  border: 4px solid #f0f0f0;
  transition: all 0.2s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  border-color: #1890ff;
}

.avatar-placeholder {
  font-size: 32px;
  font-weight: 600;
}

.change-avatar-btn {
  background: #f0f8ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.change-avatar-btn:hover {
  background: #1890ff;
  color: white;
}

.avatar-input-section {
  margin-top: 16px;
  text-align: left;
}

.avatar-input-section label {
  display: block;
  color: #262626;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
}

/* 表单区域 */
.profile-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  background: #fafafa;
  border-radius: 12px;
  padding: 20px;
}

.form-section h4 {
  color: #262626;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e6f7ff;
}

.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  color: #262626;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  color: #262626;
  background: white;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.form-input.readonly {
  background: #f5f5f5;
  color: #8c8c8c;
  cursor: not-allowed;
}

.help-text {
  display: block;
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
}

/* 角色信息 */
.role-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.role-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e6f7ff;
}

.role-label {
  color: #595959;
  font-size: 14px;
  font-weight: 500;
}

.role-value {
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.role-value.status-pending {
  color: #fa8c16;
}

.role-value.status-approved {
  color: #52c41a;
}

.role-value.status-rejected {
  color: #ff4d4f;
}

/* 表单按钮 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.btn-reset,
.btn-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-reset {
  background: #f5f5f5;
  color: #595959;
  border: 1px solid #d9d9d9;
}

.btn-reset:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.btn-submit {
  background: #1890ff;
  color: white;
}

.btn-submit:hover:not(:disabled) {
  background: #096dd9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.btn-submit:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-profile-form {
    margin: 16px;
    max-width: calc(100vw - 32px);
    max-height: calc(100vh - 32px);
  }

  .form-content {
    padding: 20px;
  }

  .avatar-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
  }

  .user-avatar {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .form-section {
    padding: 16px;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-reset,
  .btn-submit {
    width: 100%;
  }

  .role-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>

<style scoped>
.user-profile-form {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

/* 表单头部 */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.form-header h3 {
  color: #262626;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  color: #8c8c8c;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #ff4d4f;
  color: white;
}

/* 表单内容 */
.form-content {
  padding: 28px;
  overflow-y: auto;
  flex: 1;
}

/* 头像区域 */
.avatar-section {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  font-weight: 600;
  background-size: cover;
  background-position: center;
  border: 4px solid #f0f0f0;
  transition: all 0.2s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  border-color: #1890ff;
}

.avatar-placeholder {
  font-size: 32px;
  font-weight: 600;
}

.change-avatar-btn {
  background: #f0f8ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.change-avatar-btn:hover {
  background: #1890ff;
  color: white;
}

.avatar-input-section {
  margin-top: 16px;
  text-align: left;
}

.avatar-input-section label {
  display: block;
  color: #262626;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
}

/* 表单区域 */
.profile-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  background: #fafafa;
  border-radius: 12px;
  padding: 20px;
}

.form-section h4 {
  color: #262626;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e6f7ff;
}

.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  color: #262626;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  color: #262626;
  background: white;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.form-input.readonly {
  background: #f5f5f5;
  color: #8c8c8c;
  cursor: not-allowed;
}

.help-text {
  display: block;
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
}
</style>
