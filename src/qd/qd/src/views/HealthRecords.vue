<template>
  <div class="health-records-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>健康数据</h1>
        <p class="page-description">记录和追踪您的健康指标变化</p>
      </div>
      <div class="header-actions">
        <button @click="forceRefresh" class="btn-secondary" style="margin-right: 12px;">
          <span class="btn-icon">🔄</span>
          刷新数据
        </button>
        <button @click="showAddForm" class="btn-primary">
          <span class="btn-icon">📊</span>
          记录数据
        </button>
      </div>
    </div>

    <!-- 档案选择和筛选 -->
    <div class="filters-section">
      <div class="filter-group">
        <label>选择档案：</label>
        <select v-model.number="selectedProfileId" @change="handleProfileChange" class="profile-selector">
          <option :value="null">请选择健康档案</option>
          <option v-for="profile in profiles" :key="profile.id" :value="profile.id">
            {{ profile.profileOwnerName }}
          </option>
        </select>
      </div>

      <div v-if="selectedProfileId" class="filter-group">
        <label>指标类型：</label>
        <select v-model="selectedMetricType" @change="() => loadRecords(1)" class="metric-selector">
          <option value="">全部指标</option>
          <option v-for="(config, type) in METRIC_TYPES" :key="type" :value="type">
            {{ config.icon }} {{ config.label }}
          </option>
        </select>
      </div>

      <div v-if="selectedProfileId" class="filter-group">
        <label>时间范围：</label>
        <input v-model="startDate" @change="() => loadRecords(1)" type="date" class="date-input" />
        <span class="date-separator">至</span>
        <input v-model="endDate" @change="() => loadRecords(1)" type="date" class="date-input" />
      </div>
    </div>

    <!-- 主要内容 -->
    <div v-if="selectedProfileId" class="main-content">
      <!-- 图表区域 -->
      <div v-if="selectedMetricType" class="chart-section">
        <MetricChart
          :profile-id="selectedProfileId"
          :metric-type="selectedMetricType"
          :key="`${selectedProfileId}-${selectedMetricType}`"
        />
      </div>

      <!-- 记录列表 -->
      <div class="records-section">
        <div class="section-header">
          <h3>数据记录</h3>
          <div class="record-count" v-if="pagination.totalRecords > 0">
            共 {{ pagination.totalRecords }} 条记录
          </div>
        </div>

        <!-- 记录列表 -->
        <div v-if="records.length > 0" class="records-list">
          <div
            v-for="record in records"
            :key="record.id"
            class="record-item"
            @click="viewRecord(record)"
          >
            <div class="record-icon">
              {{ getMetricConfig(record.metricType).icon }}
            </div>
            <div class="record-info">
              <div class="record-type">{{ getMetricConfig(record.metricType).label }}</div>
              <div class="record-value">{{ formatRecordValue(record) }}</div>
              <div class="record-time">{{ formatDateTime(record.recordedAt) }}</div>
              <div v-if="record.notes" class="record-notes">{{ record.notes }}</div>
            </div>
            <div class="record-actions" @click.stop>
              <button @click="editRecord(record)" class="action-btn edit">
                ✏️
              </button>
              <button @click="deleteRecord(record)" class="action-btn delete">
                🗑️
              </button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else-if="!loading" class="empty-state">
          <div class="empty-icon">📊</div>
          <h3>还没有健康数据</h3>
          <p>开始记录您的健康指标，建立完整的健康档案</p>
          <button @click="showAddForm" class="btn-primary">
            记录第一条数据
          </button>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.totalPages > 1" class="pagination">
          <button
            @click="changePage(pagination.currentPage - 1)"
            :disabled="pagination.currentPage <= 1"
            class="page-btn"
          >
            上一页
          </button>
          <span class="page-info">
            第 {{ pagination.currentPage }} 页，共 {{ pagination.totalPages }} 页
          </span>
          <button
            @click="changePage(pagination.currentPage + 1)"
            :disabled="pagination.currentPage >= pagination.totalPages"
            class="page-btn"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 未选择档案的提示 -->
    <div v-else class="no-profile-selected">
      <div class="no-profile-icon">👤</div>
      <h3>请选择健康档案</h3>
      <p>选择一个健康档案来查看和记录健康数据</p>
    </div>

    <!-- 表单弹窗 -->
    <div v-if="showForm" class="form-modal">
      <div class="modal-overlay" @click="hideForm"></div>
      <div class="modal-content">
        <MetricInputForm
          :record="selectedRecord"
          :is-edit="isEditMode"
          :default-profile-id="selectedProfileId"
          @success="handleFormSuccess"
          @cancel="hideForm"
        />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <div v-if="showDetail" class="detail-modal">
      <div class="modal-overlay" @click="hideDetail"></div>
      <div class="modal-content">
        <div class="record-detail">
          <div class="detail-header">
            <h3>数据详情</h3>
            <button @click="hideDetail" class="close-btn">✕</button>
          </div>
          <div class="detail-content" v-if="selectedRecord">
            <div class="detail-icon">
              {{ getMetricConfig(selectedRecord.metricType).icon }}
            </div>
            <div class="detail-info">
              <div class="info-item">
                <label>指标类型：</label>
                <span>{{ getMetricConfig(selectedRecord.metricType).label }}</span>
              </div>
              <div class="info-item">
                <label>数值：</label>
                <span class="value-highlight">{{ formatRecordValue(selectedRecord) }}</span>
              </div>
              <div class="info-item">
                <label>记录时间：</label>
                <span>{{ formatDateTime(selectedRecord.recordedAt) }}</span>
              </div>
              <div class="info-item" v-if="selectedRecord.notes">
                <label>备注：</label>
                <span>{{ selectedRecord.notes }}</span>
              </div>
            </div>
            <div class="detail-actions">
              <button @click="editRecord(selectedRecord)" class="btn-primary">
                编辑记录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">⏳ 加载中...</div>
    </div>

    <!-- 成功通知 -->
    <div v-if="successMessage" class="success-notification">
      <div class="notification-content">
        <span class="success-icon">✅</span>
        {{ successMessage }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { getHealthProfiles } from '@/api/health'
import { getHealthRecords, deleteHealthRecord, METRIC_TYPES, getMetricConfig, formatRecordValue } from '@/api/healthRecords'
import MetricInputForm from '@/components/MetricInputForm.vue'
import MetricChart from '@/components/MetricChart.vue'

// 响应式数据
const loading = ref(false)
const successMessage = ref('')
const profiles = ref([])
const records = ref([])
const selectedProfileId = ref(null)
const selectedMetricType = ref('')
const startDate = ref('')
const endDate = ref('')
const showForm = ref(false)
const showDetail = ref(false)
const isEditMode = ref(false)
const selectedRecord = ref(null)

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  totalRecords: 0,
  totalPages: 0
})

// 方法
const loadProfiles = async () => {
  try {
    const response = await getHealthProfiles()
    console.log('档案列表响应:', response.data)

    if (response.data.code === 200) {
      // 处理不同的响应格式
      const data = response.data.data
      if (data.profiles) {
        profiles.value = data.profiles
      } else if (Array.isArray(data)) {
        profiles.value = data
      } else {
        profiles.value = []
      }

      console.log('加载的档案列表:', profiles.value)

      // 如果只有一个档案，自动选择
      if (profiles.value.length === 1) {
        selectedProfileId.value = Number(profiles.value[0].id)
        console.log('自动选择档案:', selectedProfileId.value)
        await loadRecords(1)
      } else if (profiles.value.length > 1) {
        // 如果有多个档案，选择第一个作为默认
        selectedProfileId.value = Number(profiles.value[0].id)
        console.log('默认选择第一个档案:', selectedProfileId.value)
        await loadRecords(1)
      }
    }
  } catch (error) {
    console.error('获取档案列表失败:', error)
  }
}

const loadRecords = async (page = 1) => {
  if (!selectedProfileId.value) {
    console.log('没有选择档案，跳过加载记录')
    return
  }

  loading.value = true
  console.log('开始加载记录，档案ID:', selectedProfileId.value)

  try {
    const params = {
      profileId: selectedProfileId.value,
      page: page,
      size: pagination.pageSize
    }

    if (selectedMetricType.value) {
      params.metricType = selectedMetricType.value
    }
    // 暂时移除日期筛选，避免格式问题
    // if (startDate.value) {
    //   params.startDate = startDate.value
    // }
    // if (endDate.value) {
    //   params.endDate = endDate.value
    // }

    console.log('请求参数:', params)

    const response = await getHealthRecords(params)
    console.log('记录列表响应:', response.data)

    if (response.data.code === 200) {
      const data = response.data.data
      records.value = data.records || []

      // 处理分页信息
      if (data.pagination) {
        pagination.currentPage = data.pagination.currentPage || page
        pagination.totalRecords = data.pagination.totalRecords || 0
        pagination.totalPages = data.pagination.totalPages || 0
      } else {
        pagination.currentPage = page
        pagination.totalRecords = records.value.length
        pagination.totalPages = 1
      }

      console.log('加载的记录数量:', records.value.length)
    } else {
      console.error('获取记录列表失败:', response.data.message)
      records.value = []
    }
  } catch (error) {
    console.error('获取记录列表失败:', error)
    records.value = []
  } finally {
    loading.value = false
  }
}

const handleProfileChange = () => {
  console.log('档案选择变化:', selectedProfileId.value, typeof selectedProfileId.value)
  selectedMetricType.value = ''
  records.value = []

  // 确保selectedProfileId是数字类型
  if (selectedProfileId.value && selectedProfileId.value !== '') {
    selectedProfileId.value = Number(selectedProfileId.value)
    console.log('转换后的档案ID:', selectedProfileId.value)
    loadRecords(1) // 明确传递页码1
  }
}

const forceRefresh = async () => {
  console.log('强制刷新数据...')
  await loadProfiles()
  if (selectedProfileId.value) {
    await loadRecords(1)
  }
}

const showAddForm = () => {
  selectedRecord.value = null
  isEditMode.value = false
  showForm.value = true
}

const editRecord = (record) => {
  selectedRecord.value = record
  isEditMode.value = true
  showForm.value = true
  showDetail.value = false
}

const viewRecord = (record) => {
  selectedRecord.value = record
  showDetail.value = true
}

const deleteRecord = async (record) => {
  if (!confirm(`确定要删除这条${getMetricConfig(record.metricType).label}记录吗？`)) {
    return
  }

  try {
    const response = await deleteHealthRecord(record.id)
    if (response.data.code === 200) {
      showSuccessMessage('记录删除成功！')
      await loadRecords(pagination.currentPage)
    } else {
      alert('删除失败：' + response.data.message)
    }
  } catch (error) {
    console.error('删除记录失败:', error)
    alert('删除失败，请重试')
  }
}

const hideForm = () => {
  showForm.value = false
  selectedRecord.value = null
}

const hideDetail = () => {
  showDetail.value = false
  selectedRecord.value = null
}

const handleFormSuccess = async () => {
  hideForm()
  showSuccessMessage(isEditMode.value ? '记录更新成功！' : '记录保存成功！')
  await loadRecords(pagination.currentPage)
}

const changePage = (page) => {
  if (page >= 1 && page <= pagination.totalPages) {
    loadRecords(page)
  }
}

const showSuccessMessage = (message) => {
  successMessage.value = message
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  loadProfiles()
  
  // 设置默认时间范围（最近30天）
  const today = new Date()
  const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
  
  endDate.value = today.toISOString().split('T')[0]
  startDate.value = thirtyDaysAgo.toISOString().split('T')[0]
})
</script>

<style scoped>
.health-records-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f0f0;
}

.header-content h1 {
  color: #262626;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.page-description {
  color: #8c8c8c;
  font-size: 14px;
  margin: 0;
}

.header-actions .btn-primary,
.header-actions .btn-secondary {
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions .btn-primary {
  background: #1890ff;
  color: white;
}

.header-actions .btn-secondary {
  background: #f5f5f5;
  color: #595959;
  border: 1px solid #d9d9d9;
}

.header-actions .btn-primary:hover {
  background: #096dd9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.header-actions .btn-secondary:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
  transform: translateY(-2px);
}

.btn-icon {
  font-size: 16px;
}

/* 筛选区域 */
.filters-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  color: #595959;
  font-weight: 600;
  font-size: 14px;
  white-space: nowrap;
}

.profile-selector,
.metric-selector,
.date-input {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 14px;
  color: #262626;
  cursor: pointer;
  transition: all 0.2s ease;
}

.profile-selector:hover,
.metric-selector:hover,
.date-input:hover {
  border-color: #1890ff;
}

.profile-selector:focus,
.metric-selector:focus,
.date-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.date-separator {
  color: #8c8c8c;
  font-size: 14px;
  margin: 0 4px;
}

/* 主要内容 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.chart-section {
  margin-bottom: 8px;
}

/* 记录列表区域 */
.records-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.section-header h3 {
  color: #262626;
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}

.record-count {
  color: #8c8c8c;
  font-size: 14px;
  font-weight: 500;
}

/* 记录列表 */
.records-list {
  padding: 0;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 20px 28px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.record-item:hover {
  background: #fafafa;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  font-size: 32px;
  margin-right: 16px;
  width: 48px;
  text-align: center;
}

.record-info {
  flex: 1;
}

.record-type {
  color: #262626;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.record-value {
  color: #1890ff;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 4px;
}

.record-time {
  color: #8c8c8c;
  font-size: 13px;
  margin-bottom: 4px;
}

.record-notes {
  color: #595959;
  font-size: 13px;
  font-style: italic;
}

.record-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action-btn.edit {
  background: #e6f7ff;
  color: #1890ff;
}

.action-btn.edit:hover {
  background: #1890ff;
  color: white;
}

.action-btn.delete {
  background: #fff2f0;
  color: #ff4d4f;
}

.action-btn.delete:hover {
  background: #ff4d4f;
  color: white;
}

/* 空状态 */
.empty-state,
.no-profile-selected {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon,
.no-profile-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-state h3,
.no-profile-selected h3 {
  color: #262626;
  font-size: 20px;
  margin-bottom: 12px;
}

.empty-state p,
.no-profile-selected p {
  color: #8c8c8c;
  margin-bottom: 24px;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 24px;
  border-top: 1px solid #f0f0f0;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #595959;
  font-size: 14px;
}

/* 弹窗 */
.form-modal,
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  z-index: 1001;
}

/* 详情弹窗 */
.record-detail {
  background: white;
  border-radius: 16px;
  overflow: hidden;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.detail-header h3 {
  color: #262626;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #ff4d4f;
  color: white;
}

.detail-content {
  padding: 28px;
}

.detail-icon {
  text-align: center;
  font-size: 48px;
  margin-bottom: 24px;
}

.detail-info {
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

.info-item label {
  color: #595959;
  font-weight: 600;
  min-width: 100px;
  font-size: 14px;
}

.info-item span {
  color: #262626;
  font-size: 14px;
  flex: 1;
}

.value-highlight {
  color: #1890ff !important;
  font-size: 18px !important;
  font-weight: 700 !important;
}

.detail-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-spinner {
  font-size: 18px;
  color: #1890ff;
  font-weight: 600;
}

/* 成功通知 */
.success-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 3000;
  animation: slideInRight 0.3s ease;
}

.notification-content {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.2);
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.success-icon {
  font-size: 16px;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .health-records-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .filters-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .filter-group {
    width: 100%;
    justify-content: space-between;
  }

  .profile-selector,
  .metric-selector {
    flex: 1;
    min-width: 0;
  }

  .record-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .record-actions {
    align-self: flex-end;
  }

  .modal-content {
    width: 95%;
  }

  .pagination {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
