# 后端需要修改的问题

## 🚨 紧急问题：预约状态枚举值不匹配

### 错误信息
```
No enum constant com.ruanjianjiaGou.ruanjianjiaGou.entity.Appointment.AppointmentStatus.booked
```

### 问题分析
后端Java枚举 `AppointmentStatus` 中没有 `booked` 这个值，但前端和API调用中使用了这个值。

### 🛠️ 解决方案1：修改后端枚举（推荐）

在 `AppointmentStatus` 枚举中添加缺失的状态值：

```java
public enum AppointmentStatus {
    BOOKED,     // 已预约 - 主要使用这个
    COMPLETED,  // 已完成
    CANCELLED,  // 已取消
    
    // 如果需要兼容小写，可以添加：
    booked,     // 兼容小写格式
    completed,  // 兼容小写格式
    cancelled   // 兼容小写格式
}
```

### 🛠️ 解决方案2：统一使用大写格式（推荐）

确保所有地方都使用大写格式：
- `BOOKED` - 已预约
- `COMPLETED` - 已完成  
- `CANCELLED` - 已取消

**需要检查的文件**：
1. `Appointment.java` - 实体类
2. `AppointmentStatus.java` - 枚举类
3. 所有使用预约状态的Service和Controller类
4. 数据库中的预约记录状态值

## 📅 排班API问题

### 问题现象
排班API返回空数据：
```json
{
  "code": 200,
  "message": "获取成功", 
  "data": []
}
```

### 可能原因
1. **参数格式问题** - API期望的参数名称可能不是 `startDate/endDate`
2. **日期格式问题** - 日期格式可能不匹配
3. **医生ID问题** - 医生ID可能不存在或无权限
4. **数据库数据问题** - 可能没有对应的排班数据

### 🔍 需要检查的地方

#### 1. 排班API参数格式
检查 `getDoctorSchedules` 接口期望的参数：
```java
// 可能的参数格式：
// 格式1: startDate + endDate
// 格式2: date  
// 格式3: scheduleDate
```

#### 2. 数据库排班数据
检查 `doctor_schedules` 表：
```sql
-- 检查是否有排班数据
SELECT * FROM doctor_schedules WHERE doctor_id = 7;

-- 检查日期格式
SELECT schedule_date, start_time, end_time, total_slots, booked_slots 
FROM doctor_schedules 
WHERE doctor_id = 7 AND schedule_date = '2025-06-14';
```

#### 3. 医生信息
检查医生ID是否正确：
```sql
-- 检查医生是否存在
SELECT id, user_id, real_name, status FROM doctors WHERE user_id = 7;
```

## 🏥 科室数据不完整问题

### 问题现象
数据库有9个科室，但API只返回3个。

### 🔍 需要检查的地方

#### 1. 科室查询API
检查 `getDepartments` 接口的实现：
```java
// 可能的问题：
// 1. 有分页限制但没有传递正确的分页参数
// 2. 有状态过滤条件
// 3. 有权限限制
```

#### 2. 数据库查询
检查实际的SQL查询：
```sql
-- 检查所有科室
SELECT * FROM departments;

-- 检查是否有状态字段影响查询
SELECT * FROM departments WHERE status = 'ACTIVE' OR status IS NULL;
```

## 🧪 调试建议

### 1. 使用前端调试页面
访问 `http://localhost:5173/debug-api` 进行API测试

### 2. 后端日志
在相关的Controller和Service中添加详细日志：
```java
@GetMapping("/doctors/{doctorId}/schedules")
public ResponseEntity<?> getDoctorSchedules(
    @PathVariable Long doctorId,
    @RequestParam Map<String, String> params) {
    
    log.info("获取医生排班 - 医生ID: {}, 参数: {}", doctorId, params);
    
    // ... 业务逻辑
    
    log.info("返回排班数据: {}", result);
    return ResponseEntity.ok(result);
}
```

### 3. 数据库检查脚本
```sql
-- 检查预约状态值
SELECT DISTINCT status FROM appointments;

-- 检查排班数据
SELECT d.real_name, ds.schedule_date, ds.start_time, ds.total_slots, ds.booked_slots
FROM doctor_schedules ds
JOIN doctors d ON ds.doctor_id = d.id
WHERE ds.schedule_date >= CURDATE()
ORDER BY ds.schedule_date, ds.start_time;

-- 检查科室数据
SELECT id, name, description FROM departments ORDER BY id;
```

## ✅ 前端已修复的问题

1. **预约状态兼容性** - 已支持大写和小写格式
2. **排班API调试** - 增加了多种参数格式测试
3. **健康档案数据格式** - 增加了兼容性处理
4. **详细错误日志** - 便于问题定位

## 🎯 优先级

1. **高优先级** - 修复预约状态枚举值问题
2. **中优先级** - 解决排班API参数格式问题  
3. **低优先级** - 完善科室数据查询

请按照上述建议检查和修改后端代码，然后我们可以进一步测试和优化。
