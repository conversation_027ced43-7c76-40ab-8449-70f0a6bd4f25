<template>
  <div class="consultation-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>在线问诊</h1>
      <p class="page-description">与专业医生进行在线图文咨询</p>

      <!-- 调试信息 -->
      <div class="debug-info" style="background: #f0f8ff; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #007bff;">
        <h4 style="margin: 0 0 10px 0; color: #007bff;">🔍 API调试信息</h4>
        <div style="font-family: monospace; font-size: 12px; line-height: 1.4;">
          <div><strong>用户角色:</strong> {{ userStore.userRole }}</div>
          <div><strong>是否为居民:</strong> {{ userStore.isResident ? '✅ 是' : '❌ 否' }}</div>
          <div><strong>问诊列表加载状态:</strong> {{ loading ? '🔄 加载中' : '✅ 完成' }}</div>
          <div><strong>问诊数量:</strong> {{ consultations.length }} 条</div>
          <div><strong>医生列表加载状态:</strong> {{ loadingDoctors ? '🔄 加载中' : '✅ 完成' }}</div>
          <div><strong>医生数量:</strong> {{ doctors.length }} 位</div>
          <div><strong>当前功能:</strong> {{ activeFunction === 'consultations' ? '我的问诊' : '选择医生' }}</div>
        </div>
      </div>
    </div>

    <!-- 功能选择 -->
    <div class="function-tabs">
      <button
        @click="activeFunction = 'consultations'"
        class="function-tab"
        :class="{ active: activeFunction === 'consultations' }"
      >
        <span class="tab-icon">💬</span>
        我的问诊
      </button>
      <button
        @click="activeFunction = 'doctors'"
        class="function-tab"
        :class="{ active: activeFunction === 'doctors' }"
      >
        <span class="tab-icon">👨‍⚕️</span>
        选择医生
      </button>
    </div>

    <!-- 我的问诊列表 -->
    <div v-if="activeFunction === 'consultations'" class="consultation-list">
      <div class="list-header">
        <h2>我的问诊</h2>
        <button @click="activeFunction = 'doctors'" class="create-btn">
          <span class="btn-icon">💬</span>
          发起问诊
        </button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载问诊记录...</p>
      </div>

      <!-- 问诊列表内容 -->
      <div v-else-if="consultations.length > 0" class="consultation-cards">
        <div 
          v-for="consultation in consultations" 
          :key="consultation.id" 
          class="consultation-card"
          @click="openChat(consultation)"
        >
          <div class="card-header">
            <div class="doctor-info">
              <div class="doctor-avatar">{{ consultation.doctorName?.charAt(0) || 'D' }}</div>
              <div class="doctor-details">
                <div class="doctor-name">{{ consultation.doctorName }}</div>
                <div class="doctor-dept">{{ consultation.departmentName }}</div>
              </div>
            </div>
            <div class="consultation-status" :class="consultation.status.toLowerCase()">
              {{ consultation.statusDescription }}
            </div>
          </div>
          
          <div class="card-content">
            <div class="last-message">{{ consultation.lastMessage }}</div>
            <div class="message-info">
              <span class="message-count">{{ consultation.messageCount }} 条消息</span>
              <span class="last-time">{{ formatTime(consultation.lastMessageTime) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">💬</div>
        <h3>还没有问诊记录</h3>
        <p>点击"发起问诊"开始与医生交流</p>
        <button @click="showCreateDialog = true" class="empty-action-btn">
          发起问诊
        </button>
      </div>
    </div>

    <!-- 医生选择页面 -->
    <div v-else-if="activeFunction === 'doctors'" class="doctors-selection">
      <div class="list-header">
        <h2>选择医生</h2>
        <p class="header-desc">选择合适的医生开始在线问诊</p>
      </div>

      <!-- 医生筛选器 -->
      <div class="doctor-filters">
        <div class="filter-group">
          <label>科室筛选:</label>
          <select v-model="doctorFilters.department" @change="loadDoctors" class="filter-select">
            <option value="">全部科室</option>
            <option v-for="dept in departments" :key="dept.id" :value="dept.name">
              {{ dept.name }}
            </option>
          </select>
        </div>

        <div class="filter-group">
          <label>职称筛选:</label>
          <select v-model="doctorFilters.title" @change="loadDoctors" class="filter-select">
            <option value="">全部职称</option>
            <option value="主任医师">主任医师</option>
            <option value="副主任医师">副主任医师</option>
            <option value="主治医师">主治医师</option>
            <option value="住院医师">住院医师</option>
          </select>
        </div>

        <div class="filter-group">
          <label>搜索医生:</label>
          <input
            v-model="doctorFilters.search"
            @input="loadDoctors"
            type="text"
            placeholder="输入医生姓名搜索..."
            class="filter-input"
          >
        </div>
      </div>

      <!-- 医生列表 -->
      <div class="doctors-grid">
        <div v-if="loadingDoctors" class="loading-container">
          <div class="loading-spinner"></div>
          <p>正在加载医生信息...</p>
        </div>

        <div v-else-if="filteredDoctors.length === 0" class="empty-state">
          <div class="empty-icon">👨‍⚕️</div>
          <h3>暂无医生</h3>
          <p>当前筛选条件下没有找到医生</p>
        </div>

        <div v-else class="doctor-cards">
          <div
            v-for="doctor in filteredDoctors"
            :key="doctor.id"
            class="doctor-card"
            @click="selectDoctor(doctor)"
          >
            <div class="doctor-avatar-large">
              <img v-if="doctor.avatarUrl" :src="doctor.avatarUrl" :alt="doctor.nickname">
              <span v-else>{{ doctor.nickname?.charAt(0) || 'D' }}</span>
            </div>

            <div class="doctor-info">
              <h3 class="doctor-name">{{ doctor.nickname }}</h3>
              <div class="doctor-details">
                <div class="doctor-dept">{{ doctor.departmentName }}</div>
                <div class="doctor-title">{{ doctor.title }}</div>
              </div>

              <div class="doctor-stats" v-if="doctor.stats">
                <div class="stat-item">
                  <span class="stat-label">服务患者:</span>
                  <span class="stat-value">{{ doctor.stats.totalPatients || 0 }}人</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">好评率:</span>
                  <span class="stat-value">{{ doctor.stats.rating || '95' }}%</span>
                </div>
              </div>

              <div class="doctor-status">
                <span class="status-indicator online"></span>
                <span class="status-text">在线</span>
              </div>
            </div>

            <div class="doctor-actions">
              <button @click.stop="selectDoctor(doctor)" class="consult-btn">
                立即问诊
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 发起问诊对话框 -->
    <div v-if="showCreateDialog" class="dialog-overlay" @click="closeCreateDialog">
      <div class="dialog-content" @click.stop>
        <div class="dialog-header">
          <h3>发起问诊</h3>
          <button @click="closeCreateDialog" class="close-btn">×</button>
        </div>
        
        <div class="dialog-body">
          <!-- 选中的医生信息 -->
          <div v-if="selectedDoctor" class="selected-doctor">
            <h4>选中的医生</h4>
            <div class="doctor-info-card">
              <div class="doctor-avatar-small">
                <img v-if="selectedDoctor.avatarUrl" :src="selectedDoctor.avatarUrl" :alt="selectedDoctor.nickname">
                <span v-else>{{ selectedDoctor.nickname?.charAt(0) || 'D' }}</span>
              </div>
              <div class="doctor-details">
                <div class="doctor-name">{{ selectedDoctor.nickname }}</div>
                <div class="doctor-dept">{{ selectedDoctor.departmentName }}</div>
                <div class="doctor-title">{{ selectedDoctor.title }}</div>
              </div>
              <button @click="changeDoctor" class="change-doctor-btn">
                更换医生
              </button>
            </div>
          </div>

          <!-- 选择医生 (当没有选中医生时显示) -->
          <div v-else class="form-group">
            <label>选择医生</label>
            <select v-model="createForm.doctorId" class="form-select">
              <option value="">请选择医生</option>
              <option
                v-for="doctor in doctors"
                :key="doctor.id"
                :value="doctor.id"
              >
                {{ doctor.nickname }} - {{ doctor.departmentName }}
              </option>
            </select>
          </div>

          <!-- 初始消息 -->
          <div class="form-group">
            <label>描述您的症状</label>
            <textarea
              v-model="createForm.initialMessage"
              class="form-textarea"
              placeholder="请详细描述您的症状、持续时间等信息，以便医生更好地为您提供建议..."
              rows="4"
            ></textarea>
          </div>
        </div>

        <div class="dialog-footer">
          <button @click="closeCreateDialog" class="cancel-btn">取消</button>
          <button 
            @click="createConsultation" 
            class="confirm-btn"
            :disabled="!createForm.doctorId || !createForm.initialMessage || creating"
          >
            {{ creating ? '发起中...' : '发起问诊' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 聊天对话框 -->
    <div v-if="showChatDialog" class="dialog-overlay" @click="closeChatDialog">
      <div class="chat-dialog" @click.stop>
        <div class="chat-header">
          <div class="chat-doctor-info">
            <div class="doctor-avatar">{{ currentConsultation?.doctorName?.charAt(0) || 'D' }}</div>
            <div class="doctor-details">
              <div class="doctor-name">{{ currentConsultation?.doctorName }}</div>
              <div class="doctor-dept">{{ currentConsultation?.departmentName }}</div>
            </div>
          </div>
          <div class="chat-status" :class="currentConsultation?.status?.toLowerCase()">
            {{ currentConsultation?.statusDescription }}
          </div>
          <button @click="closeChatDialog" class="close-btn">×</button>
        </div>

        <div class="chat-messages" ref="messagesContainer">
          <div v-if="loadingMessages" class="loading-messages">
            <div class="loading-spinner small"></div>
            <span>加载消息中...</span>
          </div>
          
          <div 
            v-for="message in messages" 
            :key="message.id" 
            class="message-item"
            :class="{ 'own-message': message.senderRole === 'RESIDENT' }"
          >
            <div class="message-avatar">
              {{ message.senderNickname?.charAt(0) || 'U' }}
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="sender-name">{{ message.senderNickname }}</span>
                <span class="message-time">{{ formatTime(message.sentAt) }}</span>
              </div>
              <div class="message-text">{{ message.content }}</div>
            </div>
          </div>
        </div>

        <div class="chat-input" v-if="currentConsultation?.status === 'IN_PROGRESS'">
          <div class="input-container">
            <textarea 
              v-model="newMessage" 
              class="message-input"
              placeholder="输入您的消息..."
              rows="2"
              @keydown.enter.prevent="sendMessage"
            ></textarea>
            <button 
              @click="sendMessage" 
              class="send-btn"
              :disabled="!newMessage.trim() || sending"
            >
              {{ sending ? '发送中...' : '发送' }}
            </button>
          </div>
        </div>

        <div v-else class="chat-completed">
          <div class="completed-notice">
            <span class="completed-icon">✅</span>
            <span>问诊已完成</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import { 
  getConsultations, 
  createConsultation as createConsultationApi,
  getMessages,
  sendMessage as sendMessageApi,
  getDoctorList
} from '@/api/consultation'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const consultations = ref([])
const doctors = ref([])
const departments = ref([])

// 功能切换
const activeFunction = ref('consultations')

// 医生筛选相关
const loadingDoctors = ref(false)
const doctorFilters = ref({
  department: '',
  title: '',
  search: ''
})

// 发起问诊相关
const showCreateDialog = ref(false)
const creating = ref(false)
const createForm = ref({
  doctorId: '',
  initialMessage: ''
})
const selectedDoctor = ref(null)

// 聊天相关
const showChatDialog = ref(false)
const currentConsultation = ref(null)
const messages = ref([])
const loadingMessages = ref(false)
const newMessage = ref('')
const sending = ref(false)
const messagesContainer = ref(null)

// 计算属性
const filteredDoctors = computed(() => {
  let filtered = doctors.value

  // 按科室筛选
  if (doctorFilters.value.department) {
    filtered = filtered.filter(doctor =>
      doctor.departmentName === doctorFilters.value.department
    )
  }

  // 按职称筛选
  if (doctorFilters.value.title) {
    filtered = filtered.filter(doctor =>
      doctor.title === doctorFilters.value.title
    )
  }

  // 按姓名搜索
  if (doctorFilters.value.search) {
    const searchTerm = doctorFilters.value.search.toLowerCase()
    filtered = filtered.filter(doctor =>
      doctor.nickname?.toLowerCase().includes(searchTerm) ||
      doctor.realName?.toLowerCase().includes(searchTerm)
    )
  }

  return filtered
})

// 方法
const loadConsultations = async () => {
  loading.value = true
  console.log('=== 开始加载问诊列表 ===')
  try {
    const response = await getConsultations({ page: 1, size: 20 })
    console.log('问诊列表API响应:', response)
    if (response.data.code === 200) {
      consultations.value = response.data.data.content
      console.log('问诊列表加载成功:', consultations.value)
    } else {
      console.error('问诊列表API返回错误:', response.data)
    }
  } catch (error) {
    console.error('加载问诊列表失败:', error)
    console.error('错误详情:', error.response?.data || error.message)

    // 检查是否是枚举值错误
    if (error.response?.data?.message?.includes('enum constant')) {
      console.error('🚨 检测到枚举值错误！需要修复数据库')
      alert('系统检测到数据库配置问题，请联系管理员修复枚举值设置')
    }
  } finally {
    loading.value = false
  }
}

const loadDoctors = async () => {
  loadingDoctors.value = true
  console.log('=== 开始加载医生列表 ===')
  try {
    const params = {
      page: 1,
      size: 100
    }

    // 添加筛选参数 - 修正参数名称
    if (doctorFilters.value.department) {
      params.department = doctorFilters.value.department
    }
    if (doctorFilters.value.title) {
      params.title = doctorFilters.value.title
    }
    if (doctorFilters.value.search && doctorFilters.value.search.trim()) {
      params.name = doctorFilters.value.search.trim() // 使用name参数而不是search
    }

    console.log('医生列表API请求参数:', params)
    const response = await getDoctorList(params)
    console.log('医生列表API响应:', response)

    if (response.data.code === 200) {
      // 处理响应数据结构
      const data = response.data.data
      if (Array.isArray(data)) {
        doctors.value = data
      } else if (data && data.content) {
        doctors.value = data.content
      } else {
        doctors.value = []
      }
      console.log('医生列表加载成功:', doctors.value.length, '位医生')
    } else {
      console.error('医生列表API返回错误:', response.data)
      alert(response.data.message || '加载医生列表失败')
      doctors.value = []
    }
  } catch (error) {
    console.error('加载医生列表失败:', error)
    console.error('错误详情:', error.response?.data || error.message)

    // 如果是参数错误，提供更友好的错误信息
    if (error.response?.status === 500 && error.response?.data?.message?.includes('NumberFormatException')) {
      console.error('🚨 API参数格式错误，可能是后端接口参数不匹配')
      alert('医生列表加载失败：参数格式错误，请联系管理员')
    } else if (error.response?.status === 404) {
      console.error('🚨 医生列表API不存在')
      alert('医生列表功能暂时不可用')
    } else {
      alert('加载医生列表失败，请稍后重试')
    }
    doctors.value = []
  } finally {
    loadingDoctors.value = false
  }
}

const loadDepartments = async () => {
  try {
    // 从医生列表中提取科室信息
    const response = await getDoctorList({ page: 1, size: 1000 })
    if (response.data.code === 200) {
      // 处理响应数据结构
      const data = response.data.data
      let allDoctors = []

      if (Array.isArray(data)) {
        allDoctors = data
      } else if (data && data.content) {
        allDoctors = data.content
      }

      const deptSet = new Set()
      allDoctors.forEach(doctor => {
        if (doctor.departmentName) {
          deptSet.add(doctor.departmentName)
        }
      })
      departments.value = Array.from(deptSet).map((name, index) => ({
        id: index + 1,
        name
      }))
      console.log('科室列表加载成功:', departments.value)
    }
  } catch (error) {
    console.error('加载科室列表失败:', error)
    // 设置默认科室列表
    departments.value = [
      { id: 1, name: '内科' },
      { id: 2, name: '外科' },
      { id: 3, name: '儿科' },
      { id: 4, name: '妇科' }
    ]
  }
}

const selectDoctor = (doctor) => {
  selectedDoctor.value = doctor
  // 根据API测试报告，医生ID可能是userId字段
  createForm.value.doctorId = doctor.userId || doctor.id
  console.log('选择医生:', doctor.realName || doctor.nickname, '医生ID:', createForm.value.doctorId)
  showCreateDialog.value = true
}

const createConsultation = async () => {
  creating.value = true
  console.log('=== 创建问诊会话（API测试报告格式）===')

  try {
    // 根据API测试报告，确保请求数据格式正确
    const requestData = {
      doctorId: parseInt(createForm.value.doctorId) // 确保是数字类型
    }

    console.log('请求数据:', requestData)
    console.log('医生ID类型:', typeof requestData.doctorId)

    const response = await createConsultationApi(requestData)
    console.log('=== 创建问诊API响应 ===')
    console.log('响应状态:', response.status)
    console.log('响应数据:', response.data)

    if (response.data.code === 200) {
      const consultationData = response.data.data
      console.log('✅ 问诊会话创建成功')
      console.log('问诊ID:', consultationData.id)
      console.log('用户昵称:', consultationData.userNickname)
      console.log('医生姓名:', consultationData.doctorName)
      console.log('科室名称:', consultationData.departmentName)
      console.log('状态:', consultationData.status)
      console.log('状态描述:', consultationData.statusDescription)
      console.log('消息数量:', consultationData.messageCount)

      alert('问诊会话创建成功！')
      closeCreateDialog()
      await loadConsultations()

      // 直接打开新创建的问诊
      openChat(consultationData)
    } else {
      console.error('❌ 创建问诊API返回错误:', response.data)
      alert(response.data.message || '创建问诊失败')
    }
  } catch (error) {
    console.error('❌ 创建问诊失败:', error)
    console.error('错误详情:', error.response?.data || error.message)
    console.error('错误状态码:', error.response?.status)

    if (error.response?.status === 401) {
      alert('登录已过期，请重新登录')
      userStore.logout()
      router.push('/login')
    } else if (error.response?.status === 403) {
      alert('没有权限创建问诊')
    } else if (error.response?.status === 400) {
      alert('请求参数错误：' + (error.response?.data?.message || '请检查医生选择'))
    } else {
      alert('创建问诊失败：' + (error.response?.data?.message || '请重试'))
    }
  } finally {
    creating.value = false
  }
}

const openChat = async (consultation) => {
  currentConsultation.value = consultation
  showChatDialog.value = true
  await loadMessages()
}

const loadMessages = async () => {
  if (!currentConsultation.value) return
  
  loadingMessages.value = true
  try {
    const response = await getMessages(currentConsultation.value.id, { page: 1, size: 50 })
    if (response.data.code === 200) {
      messages.value = response.data.data.content
      await nextTick()
      scrollToBottom()
    }
  } catch (error) {
    console.error('加载消息失败:', error)
  } finally {
    loadingMessages.value = false
  }
}

const sendMessage = async () => {
  if (!newMessage.value.trim() || !currentConsultation.value) return
  
  sending.value = true
  try {
    const response = await sendMessageApi(currentConsultation.value.id, {
      content: newMessage.value.trim()
    })
    if (response.data.code === 200) {
      messages.value.push(response.data.data)
      newMessage.value = ''
      await nextTick()
      scrollToBottom()
      // 更新问诊列表中的最后消息
      loadConsultations()
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    console.error('错误详情:', error.response?.data || error.message)

    // 检查是否是枚举值错误
    if (error.response?.data?.message?.includes('enum constant')) {
      console.error('🚨 检测到枚举值错误！需要修复数据库')
      alert('系统检测到数据库配置问题，请联系管理员修复枚举值设置')
    } else {
      alert('发送消息失败，请重试')
    }
  } finally {
    sending.value = false
  }
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const changeDoctor = () => {
  selectedDoctor.value = null
  createForm.value.doctorId = ''
  showCreateDialog.value = false
  activeFunction.value = 'doctors'
}

const closeCreateDialog = () => {
  showCreateDialog.value = false
  selectedDoctor.value = null
  createForm.value = {
    doctorId: '',
    initialMessage: ''
  }
}

const closeChatDialog = () => {
  showChatDialog.value = false
  currentConsultation.value = null
  messages.value = []
  newMessage.value = ''
}

const formatTime = (timeString) => {
  if (!timeString) return ''
  const date = new Date(timeString)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  // 检查用户权限
  if (!userStore.isLoggedIn) {
    router.push('/login')
    return
  }

  if (!userStore.isResident) {
    alert('权限不足，需要居民身份')
    router.push('/')
    return
  }

  loadConsultations()
  loadDoctors()
  loadDepartments()
})
</script>

<style scoped>
.consultation-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 32px;
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 功能选择标签 */
.function-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  background: white;
  padding: 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.function-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: transparent;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.function-tab:hover {
  background: #f1f5f9;
  color: #3b82f6;
}

.function-tab.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.tab-icon {
  font-size: 18px;
}

.page-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.page-description {
  color: #64748b;
  font-size: 16px;
  margin: 0;
}

/* 问诊列表 */
.consultation-list {
  max-width: 1200px;
  margin: 0 auto;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.list-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.create-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.btn-icon {
  font-size: 18px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
  margin: 0 8px 0 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 问诊卡片 */
.consultation-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.consultation-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.consultation-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.doctor-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.doctor-avatar {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
}

.doctor-details {
  flex: 1;
}

.doctor-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.doctor-dept {
  font-size: 14px;
  color: #64748b;
}

.consultation-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.consultation-status.in_progress {
  background: #dbeafe;
  color: #1d4ed8;
}

.consultation-status.completed {
  background: #dcfce7;
  color: #16a34a;
}

.card-content {
  border-top: 1px solid #f1f5f9;
  padding-top: 16px;
}

.last-message {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.message-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #9ca3af;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.empty-state p {
  color: #64748b;
  margin: 0 0 24px 0;
}

.empty-action-btn {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.empty-action-btn:hover {
  background: #2563eb;
}

/* 医生选择页面样式 */
.doctors-selection {
  max-width: 1200px;
  margin: 0 auto;
}

.header-desc {
  color: #64748b;
  font-size: 16px;
  margin: 8px 0 0 0;
}

/* 医生筛选器 */
.doctor-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
}

.filter-group label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.filter-select,
.filter-input {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.filter-select:focus,
.filter-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 医生网格 */
.doctors-grid {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.doctor-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.doctor-card {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.doctor-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  border-color: #10b981;
}

.doctor-avatar-large {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 32px;
  margin: 0 auto 16px auto;
  overflow: hidden;
}

.doctor-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.doctor-info h3 {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.doctor-details {
  margin-bottom: 16px;
}

.doctor-dept {
  font-size: 16px;
  color: #3b82f6;
  font-weight: 600;
  margin-bottom: 4px;
}

.doctor-title {
  font-size: 14px;
  color: #64748b;
}

.doctor-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.doctor-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin-bottom: 16px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.online {
  background: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
}

.status-text {
  font-size: 14px;
  color: #10b981;
  font-weight: 600;
}

.consult-btn {
  width: 100%;
  padding: 12px 24px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.consult-btn:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
}

/* 对话框 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.dialog-content {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #f1f5f9;
}

.dialog-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f1f5f9;
  border-radius: 50%;
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e2e8f0;
  color: #374151;
}

.dialog-body {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s ease;
}

.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.2s ease;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #f1f5f9;
}

.cancel-btn {
  padding: 10px 20px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.confirm-btn {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-btn:hover:not(:disabled) {
  background: #2563eb;
}

.confirm-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 选中医生信息卡片 */
.selected-doctor {
  margin-bottom: 20px;
}

.selected-doctor h4 {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.doctor-info-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 12px;
}

.doctor-avatar-small {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
  overflow: hidden;
  flex-shrink: 0;
}

.doctor-avatar-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.doctor-info-card .doctor-details {
  flex: 1;
}

.doctor-info-card .doctor-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.doctor-info-card .doctor-dept {
  font-size: 14px;
  color: #3b82f6;
  font-weight: 500;
  margin-bottom: 2px;
}

.doctor-info-card .doctor-title {
  font-size: 13px;
  color: #64748b;
}

.change-doctor-btn {
  padding: 8px 16px;
  background: white;
  color: #3b82f6;
  border: 1px solid #3b82f6;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.change-doctor-btn:hover {
  background: #3b82f6;
  color: white;
}

/* 聊天对话框 */
.chat-dialog {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 800px;
  height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f1f5f9;
  background: #f8fafc;
}

.chat-doctor-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.chat-status.in_progress {
  background: #dbeafe;
  color: #1d4ed8;
}

.chat-status.completed {
  background: #dcfce7;
  color: #16a34a;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f8fafc;
}

.loading-messages {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #64748b;
}

.message-item {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.message-item.own-message {
  flex-direction: row-reverse;
}

.message-item.own-message .message-content {
  background: #3b82f6;
  color: white;
}

.message-item.own-message .message-header .sender-name {
  color: rgba(255, 255, 255, 0.9);
}

.message-item.own-message .message-header .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.message-avatar {
  width: 40px;
  height: 40px;
  background: #e5e7eb;
  color: #6b7280;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.own-message .message-avatar {
  background: #1d4ed8;
  color: white;
}

.message-content {
  background: white;
  border-radius: 16px;
  padding: 12px 16px;
  max-width: 70%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.sender-name {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.message-time {
  font-size: 11px;
  color: #9ca3af;
}

.message-text {
  font-size: 14px;
  line-height: 1.5;
  color: #1f2937;
  word-wrap: break-word;
}

.own-message .message-text {
  color: white;
}

.chat-input {
  padding: 20px;
  border-top: 1px solid #f1f5f9;
  background: white;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 12px;
  font-size: 14px;
  font-family: inherit;
  resize: none;
  max-height: 100px;
  transition: border-color 0.2s ease;
}

.message-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.send-btn {
  padding: 12px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.send-btn:hover:not(:disabled) {
  background: #2563eb;
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.chat-completed {
  padding: 20px;
  border-top: 1px solid #f1f5f9;
  background: #f8fafc;
}

.completed-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #16a34a;
  font-weight: 600;
}

.completed-icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .consultation-page {
    padding: 16px;
  }

  .consultation-cards {
    grid-template-columns: 1fr;
  }

  .list-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .dialog-overlay {
    padding: 10px;
  }

  .chat-dialog {
    height: 90vh;
  }

  .message-content {
    max-width: 85%;
  }

  .function-tabs {
    flex-direction: column;
    gap: 8px;
  }

  .function-tab {
    flex: none;
  }

  .doctor-filters {
    flex-direction: column;
    gap: 16px;
  }

  .filter-group {
    min-width: auto;
  }

  .doctor-cards {
    grid-template-columns: 1fr;
  }

  .doctor-stats {
    flex-direction: column;
    gap: 8px;
  }

  .doctor-info-card {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
}
</style>
