<template>
  <div class="metric-input-form">
    <div class="form-header">
      <h3>{{ isEdit ? '编辑健康数据' : '记录健康数据' }}</h3>
      <p class="form-description">记录您的健康指标数据，建立完整的健康档案</p>
    </div>

    <form @submit.prevent="handleSubmit" class="form-content">
      <!-- 档案选择 -->
      <div class="form-group">
        <label for="profile" class="form-label required">选择档案</label>
        <select
          id="profile"
          v-model="formData.profileId"
          :class="{ 'error': errors.profileId }"
          @change="validateField('profileId')"
          :disabled="isEdit"
        >
          <option value="">请选择健康档案</option>
          <option v-for="profile in profiles" :key="profile.id" :value="profile.id">
            {{ profile.profileOwnerName }}
          </option>
        </select>
        <span v-if="errors.profileId" class="error-message">
          {{ errors.profileId }}
        </span>
      </div>

      <!-- 指标类型选择 -->
      <div class="form-group">
        <label for="metricType" class="form-label required">健康指标</label>
        <div class="metric-type-grid">
          <div
            v-for="(config, type) in METRIC_TYPES"
            :key="type"
            class="metric-type-card"
            :class="{ 'selected': formData.metricType === type }"
            @click="selectMetricType(type)"
          >
            <div class="metric-icon">{{ config.icon }}</div>
            <div class="metric-label">{{ config.label }}</div>
          </div>
        </div>
        <span v-if="errors.metricType" class="error-message">
          {{ errors.metricType }}
        </span>
      </div>

      <!-- 血压专用字段 -->
      <div v-if="formData.metricType === 'blood_pressure'" class="form-row">
        <div class="form-group">
          <label for="systolic" class="form-label required">收缩压</label>
          <input
            id="systolic"
            v-model.number="formData.systolicPressure"
            type="number"
            placeholder="120"
            min="60"
            max="250"
            :class="{ 'error': errors.systolicPressure }"
            @blur="validateField('systolicPressure')"
          />
          <span v-if="errors.systolicPressure" class="error-message">
            {{ errors.systolicPressure }}
          </span>
        </div>

        <div class="form-group">
          <label for="diastolic" class="form-label required">舒张压</label>
          <input
            id="diastolic"
            v-model.number="formData.diastolicPressure"
            type="number"
            placeholder="80"
            min="40"
            max="150"
            :class="{ 'error': errors.diastolicPressure }"
            @blur="validateField('diastolicPressure')"
          />
          <span v-if="errors.diastolicPressure" class="error-message">
            {{ errors.diastolicPressure }}
          </span>
        </div>
      </div>

      <!-- 通用数值字段 -->
      <div v-else-if="formData.metricType" class="form-group">
        <label for="metricValue" class="form-label required">
          {{ getMetricConfig(formData.metricType).label }}数值
        </label>
        <div class="input-with-unit">
          <input
            id="metricValue"
            v-model.number="formData.metricValue"
            type="number"
            step="0.1"
            :placeholder="getPlaceholder()"
            :class="{ 'error': errors.metricValue }"
            @blur="validateField('metricValue')"
          />
          <span class="unit-label">{{ getMetricConfig(formData.metricType).unit }}</span>
        </div>
        <span v-if="errors.metricValue" class="error-message">
          {{ errors.metricValue }}
        </span>
      </div>

      <!-- 记录时间 -->
      <div class="form-group">
        <label for="recordedAt" class="form-label required">记录时间</label>
        <input
          id="recordedAt"
          v-model="formData.recordedAt"
          type="datetime-local"
          :max="maxDateTime"
          :class="{ 'error': errors.recordedAt }"
          @change="validateField('recordedAt')"
        />
        <span v-if="errors.recordedAt" class="error-message">
          {{ errors.recordedAt }}
        </span>
      </div>

      <!-- 备注 -->
      <div class="form-group">
        <label for="notes" class="form-label">备注</label>
        <textarea
          id="notes"
          v-model="formData.notes"
          placeholder="记录测量环境、身体状况等备注信息（可选）"
          rows="3"
        ></textarea>
      </div>

      <!-- 表单操作 -->
      <div class="form-actions">
        <button type="button" @click="handleCancel" class="btn-cancel">
          取消
        </button>
        <button type="submit" :disabled="isSubmitting" class="btn-submit">
          {{ isSubmitting ? '保存中...' : (isEdit ? '更新记录' : '保存记录') }}
        </button>
      </div>
    </form>

    <!-- 错误提示 -->
    <div v-if="submitError" class="error-alert">
      {{ submitError }}
    </div>

    <!-- 成功提示 -->
    <div v-if="submitSuccess" class="success-alert">
      {{ isEdit ? '记录更新成功！' : '记录保存成功！' }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { addHealthRecord, updateHealthRecord, METRIC_TYPES, getMetricConfig } from '@/api/healthRecords'
import { getHealthProfiles } from '@/api/health'

// Props
const props = defineProps({
  record: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  defaultProfileId: {
    type: Number,
    default: null
  }
})

// Emits
const emit = defineEmits(['success', 'cancel'])

// 响应式数据
const isSubmitting = ref(false)
const submitError = ref('')
const submitSuccess = ref(false)
const profiles = ref([])

const formData = reactive({
  profileId: '',
  metricType: '',
  metricValue: '',
  systolicPressure: '',
  diastolicPressure: '',
  unit: '',
  notes: '',
  recordedAt: ''
})

const errors = reactive({
  profileId: '',
  metricType: '',
  metricValue: '',
  systolicPressure: '',
  diastolicPressure: '',
  recordedAt: ''
})

// 计算属性
const maxDateTime = computed(() => {
  const now = new Date()
  return now.toISOString().slice(0, 16)
})

// 方法
const loadProfiles = async () => {
  try {
    const response = await getHealthProfiles()
    if (response.data.code === 200) {
      profiles.value = response.data.data.profiles || []
    }
  } catch (error) {
    console.error('获取档案列表失败:', error)
  }
}

const selectMetricType = (type) => {
  formData.metricType = type
  const config = getMetricConfig(type)
  formData.unit = config.unit
  
  // 清除之前的错误
  errors.metricType = ''
  
  // 如果是血压类型，设置metricValue为收缩压
  if (type === 'blood_pressure') {
    formData.metricValue = formData.systolicPressure || ''
  }
}

const getPlaceholder = () => {
  const placeholders = {
    blood_sugar: '5.5',
    weight: '70.0',
    heart_rate: '75',
    temperature: '36.5'
  }
  return placeholders[formData.metricType] || ''
}

const validateField = (field) => {
  errors[field] = ''

  switch (field) {
    case 'profileId':
      if (!formData.profileId) {
        errors[field] = '请选择健康档案'
      }
      break

    case 'metricType':
      if (!formData.metricType) {
        errors[field] = '请选择健康指标'
      }
      break

    case 'metricValue':
      if (!formData.metricValue || formData.metricValue <= 0) {
        errors[field] = '请输入有效的数值'
      }
      break

    case 'systolicPressure':
      if (!formData.systolicPressure || formData.systolicPressure < 60 || formData.systolicPressure > 250) {
        errors[field] = '收缩压应在60-250之间'
      } else {
        formData.metricValue = formData.systolicPressure
      }
      break

    case 'diastolicPressure':
      if (!formData.diastolicPressure || formData.diastolicPressure < 40 || formData.diastolicPressure > 150) {
        errors[field] = '舒张压应在40-150之间'
      }
      break

    case 'recordedAt':
      if (!formData.recordedAt) {
        errors[field] = '请选择记录时间'
      }
      break
  }
}

const validateForm = () => {
  validateField('profileId')
  validateField('metricType')
  validateField('recordedAt')

  if (formData.metricType === 'blood_pressure') {
    validateField('systolicPressure')
    validateField('diastolicPressure')
  } else {
    validateField('metricValue')
  }

  return !Object.values(errors).some(error => error)
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true
  submitError.value = ''
  submitSuccess.value = false

  try {
    let response
    const submitData = { ...formData }
    
    // 转换时间格式
    if (submitData.recordedAt) {
      submitData.recordedAt = new Date(submitData.recordedAt).toISOString()
    }

    if (props.isEdit && props.record?.id) {
      response = await updateHealthRecord(props.record.id, submitData)
    } else {
      response = await addHealthRecord(submitData)
    }

    if (response.data.code === 200) {
      submitSuccess.value = true
      setTimeout(() => {
        emit('success', response.data.data)
      }, 1000)
    } else {
      submitError.value = response.data.message || '操作失败'
    }
  } catch (error) {
    console.error('提交失败:', error)
    submitError.value = error.response?.data?.message || '网络错误，请重试'
  } finally {
    isSubmitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 监听props变化
watch(() => props.record, (newRecord) => {
  if (newRecord) {
    Object.assign(formData, {
      profileId: newRecord.profileId || '',
      metricType: newRecord.metricType || '',
      metricValue: newRecord.metricValue || '',
      systolicPressure: newRecord.systolicPressure || '',
      diastolicPressure: newRecord.diastolicPressure || '',
      unit: newRecord.unit || '',
      notes: newRecord.notes || '',
      recordedAt: newRecord.recordedAt ? new Date(newRecord.recordedAt).toISOString().slice(0, 16) : ''
    })
  }
}, { immediate: true })

watch(() => props.defaultProfileId, (profileId) => {
  if (profileId && !props.isEdit) {
    formData.profileId = profileId
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  loadProfiles()
  
  // 设置默认记录时间为当前时间
  if (!props.isEdit) {
    const now = new Date()
    formData.recordedAt = now.toISOString().slice(0, 16)
  }
})
</script>

<style scoped>
.metric-input-form {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-header {
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.form-header h3 {
  color: #262626;
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.form-description {
  color: #8c8c8c;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.form-content {
  padding: 28px;
}

.form-group {
  margin-bottom: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.form-label {
  display: block;
  color: #262626;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
}

.form-label.required::after {
  content: ' *';
  color: #ff4d4f;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

/* 指标类型选择 */
.metric-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 8px;
}

.metric-type-card {
  padding: 16px 12px;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.metric-type-card:hover {
  border-color: #91d5ff;
  background: #f0f8ff;
}

.metric-type-card.selected {
  border-color: #1890ff;
  background: #e6f7ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.metric-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.metric-label {
  color: #262626;
  font-size: 13px;
  font-weight: 600;
}

/* 带单位的输入框 */
.input-with-unit {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-unit input {
  padding-right: 60px;
}

.unit-label {
  position: absolute;
  right: 16px;
  color: #8c8c8c;
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
}

/* 表单操作 */
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.btn-cancel,
.btn-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-cancel {
  background: #f5f5f5;
  color: #595959;
  border: 1px solid #d9d9d9;
}

.btn-cancel:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.btn-submit {
  background: #1890ff;
  color: white;
  min-width: 120px;
}

.btn-submit:hover:not(:disabled) {
  background: #096dd9;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.btn-submit:disabled {
  background: #d9d9d9;
  color: #8c8c8c;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 提示信息 */
.error-alert,
.success-alert {
  margin: 16px 28px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.error-alert {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.success-alert {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-content {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .metric-type-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .metric-type-grid {
    grid-template-columns: 1fr;
  }
}
</style>
