<template>
  <div class="doctor-statistics">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>统计分析</h1>
      <div class="header-actions">
        <select v-model="selectedPeriod" @change="loadStatistics" class="period-selector">
          <option value="today">今日</option>
          <option value="week">本周</option>
          <option value="month">本月</option>
          <option value="quarter">本季度</option>
          <option value="year">本年</option>
          <option value="custom">自定义</option>
        </select>
        
        <div v-if="selectedPeriod === 'custom'" class="date-range">
          <input 
            type="date" 
            v-model="customDateRange.start" 
            @change="loadStatistics"
            class="date-input"
          >
          <span>至</span>
          <input 
            type="date" 
            v-model="customDateRange.end" 
            @change="loadStatistics"
            class="date-input"
          >
        </div>
        
        <button @click="exportReport" class="export-btn">
          导出报告
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <div class="loading-spinner"></div>
      <p>正在加载统计数据...</p>
    </div>

    <!-- 统计概览卡片 -->
    <div v-else class="stats-overview">
      <div class="stats-grid">
        <DoctorStatsCard
          title="总预约数"
          :value="statistics.totalAppointments || 0"
          :change="statistics.appointmentChange || 0"
          icon="📅"
          type="primary"
          format="number"
        />

        <DoctorStatsCard
          title="接诊患者"
          :value="statistics.totalPatients || 0"
          :change="statistics.patientChange || 0"
          icon="👥"
          type="success"
          format="number"
        />

        <DoctorStatsCard
          title="满意度"
          :value="statistics.satisfactionRate || 0"
          :change="statistics.satisfactionChange || 0"
          icon="⭐"
          type="warning"
          format="percentage"
        />

        <DoctorStatsCard
          title="诊疗收入"
          :value="statistics.totalIncome || 0"
          :change="statistics.incomeChange || 0"
          icon="💰"
          type="success"
          format="currency"
        />
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- 预约趋势图 -->
        <div class="chart-container">
          <h3>预约趋势</h3>
          <div ref="appointmentTrendChart" class="chart"></div>
        </div>

        <!-- 患者年龄分布 -->
        <div class="chart-container">
          <h3>患者年龄分布</h3>
          <div ref="ageDistributionChart" class="chart"></div>
        </div>
      </div>

      <div class="chart-row">
        <!-- 疾病分布 -->
        <div class="chart-container">
          <h3>疾病分布</h3>
          <div ref="diseaseDistributionChart" class="chart"></div>
        </div>

        <!-- 工作量统计 -->
        <div class="chart-container">
          <h3>工作量统计</h3>
          <div ref="workloadChart" class="chart"></div>
        </div>
      </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-tables">
      <div class="table-container">
        <h3>最近诊疗记录</h3>
        <table class="data-table">
          <thead>
            <tr>
              <th>日期</th>
              <th>患者姓名</th>
              <th>诊断</th>
              <th>治疗方案</th>
              <th>状态</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="record in recentRecords" :key="record.id">
              <td>{{ formatDate(record.date) }}</td>
              <td>{{ record.patientName }}</td>
              <td>{{ record.diagnosis }}</td>
              <td>{{ record.treatment }}</td>
              <td>
                <span class="status-badge" :class="record.status.toLowerCase()">
                  {{ getStatusText(record.status) }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 患者健康数据查看模态框 -->
    <div v-if="showPatientHealthModal" class="modal-overlay" @click="closePatientHealthModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>患者健康数据</h3>
          <button @click="closePatientHealthModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <PatientHealthChart 
            v-if="selectedPatientId" 
            :patient-id="selectedPatientId"
            :date-range="getDateRange()"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import * as doctorStatsApi from '@/api/doctorStats'
import PatientHealthChart from '@/components/PatientHealthChart.vue'
import DoctorStatsCard from '@/components/DoctorStatsCard.vue'

export default {
  name: 'DoctorStatistics',
  components: {
    PatientHealthChart,
    DoctorStatsCard
  },
  setup() {
    // 响应式数据
    const loading = ref(false)
    const selectedPeriod = ref('month')
    const customDateRange = reactive({
      start: '',
      end: ''
    })
    
    const statistics = reactive({
      totalAppointments: 0,
      totalPatients: 0,
      satisfactionRate: 0,
      totalIncome: 0,
      appointmentChange: 0,
      patientChange: 0,
      satisfactionChange: 0,
      incomeChange: 0
    })
    
    const recentRecords = ref([])
    const showPatientHealthModal = ref(false)
    const selectedPatientId = ref(null)
    
    // 图表引用
    const appointmentTrendChart = ref(null)
    const ageDistributionChart = ref(null)
    const diseaseDistributionChart = ref(null)
    const workloadChart = ref(null)
    
    // 图表实例
    let appointmentChart = null
    let ageChart = null
    let diseaseChart = null
    let workloadChartInstance = null

    // 加载统计数据
    const loadStatistics = async () => {
      try {
        loading.value = true

        const params = getQueryParams()
        const response = await doctorStatsApi.getDoctorStatistics(params)

        if (response.data.code === 200) {
          Object.assign(statistics, response.data.data)

          // 如果API返回了最近诊疗记录，使用它们
          if (response.data.data.recentRecords) {
            recentRecords.value = response.data.data.recentRecords
          }
        } else {
          console.error('获取统计数据失败:', response.data.message)
        }

        await loadChartData()
      } catch (error) {
        console.error('加载统计数据失败:', error)
        // 显示错误信息给用户
        alert('加载统计数据失败，请稍后重试')
      } finally {
        loading.value = false
      }
    }

    // 获取查询参数
    const getQueryParams = () => {
      const params = { period: selectedPeriod.value }
      
      if (selectedPeriod.value === 'custom') {
        params.startDate = customDateRange.start
        params.endDate = customDateRange.end
      }
      
      return params
    }

    // 获取日期范围
    const getDateRange = () => {
      if (selectedPeriod.value === 'custom') {
        return {
          start: customDateRange.start,
          end: customDateRange.end
        }
      }
      
      const now = new Date()
      const start = new Date()
      
      switch (selectedPeriod.value) {
        case 'today':
          start.setHours(0, 0, 0, 0)
          break
        case 'week':
          start.setDate(now.getDate() - 7)
          break
        case 'month':
          start.setMonth(now.getMonth() - 1)
          break
        case 'quarter':
          start.setMonth(now.getMonth() - 3)
          break
        case 'year':
          start.setFullYear(now.getFullYear() - 1)
          break
      }
      
      return {
        start: start.toISOString().split('T')[0],
        end: now.toISOString().split('T')[0]
      }
    }

    // 加载图表数据
    const loadChartData = async () => {
      await nextTick()

      try {
        const params = getQueryParams()

        const [trendData, ageData, diseaseData, workloadData] = await Promise.all([
          doctorStatsApi.getAppointmentTrends(params),
          doctorStatsApi.getPatientAgeDistribution(params),
          doctorStatsApi.getDiseaseDistribution(params),
          doctorStatsApi.getDoctorWorkload(params)
        ])

        // 初始化图表
        if (trendData.data.code === 200) {
          initAppointmentTrendChart(trendData.data.data)
        }
        if (ageData.data.code === 200) {
          initAgeDistributionChart(ageData.data.data)
        }
        if (diseaseData.data.code === 200) {
          initDiseaseDistributionChart(diseaseData.data.data)
        }
        if (workloadData.data.code === 200) {
          initWorkloadChart(workloadData.data.data)
        }

      } catch (error) {
        console.error('加载图表数据失败:', error)
        alert('加载图表数据失败，请稍后重试')
      }
    }

    // 初始化预约趋势图
    const initAppointmentTrendChart = (data) => {
      if (!appointmentTrendChart.value) return

      if (appointmentChart) {
        appointmentChart.dispose()
      }

      appointmentChart = echarts.init(appointmentTrendChart.value)

      const option = {
        title: {
          text: '预约趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: data?.dates || []
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '预约数量',
          type: 'line',
          data: data?.counts || [],
          smooth: true,
          itemStyle: {
            color: '#4A90E2'
          }
        }]
      }

      appointmentChart.setOption(option)
    }

    // 初始化年龄分布图
    const initAgeDistributionChart = (data) => {
      if (!ageDistributionChart.value) return

      if (ageChart) {
        ageChart.dispose()
      }

      ageChart = echarts.init(ageDistributionChart.value)

      const option = {
        title: {
          text: '患者年龄分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [{
          name: '年龄分布',
          type: 'pie',
          radius: '50%',
          data: data?.ageGroups || []
        }]
      }

      ageChart.setOption(option)
    }

    // 初始化疾病分布图
    const initDiseaseDistributionChart = (data) => {
      if (!diseaseDistributionChart.value) return

      if (diseaseChart) {
        diseaseChart.dispose()
      }

      diseaseChart = echarts.init(diseaseDistributionChart.value)

      const option = {
        title: {
          text: '疾病分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: data?.diseases || []
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '患者数量',
          type: 'bar',
          data: data?.counts || [],
          itemStyle: {
            color: '#28a745'
          }
        }]
      }

      diseaseChart.setOption(option)
    }

    // 初始化工作量图
    const initWorkloadChart = (data) => {
      if (!workloadChart.value) return

      if (workloadChartInstance) {
        workloadChartInstance.dispose()
      }

      workloadChartInstance = echarts.init(workloadChart.value)

      const option = {
        title: {
          text: '工作量统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['预约数', '完成数'],
          top: 30
        },
        xAxis: {
          type: 'category',
          data: data?.dates || []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '预约数',
            type: 'bar',
            data: data?.appointments || [],
            itemStyle: {
              color: '#4A90E2'
            }
          },
          {
            name: '完成数',
            type: 'bar',
            data: data?.completed || [],
            itemStyle: {
              color: '#28a745'
            }
          }
        ]
      }

      workloadChartInstance.setOption(option)
    }

    // 工具函数
    const formatChange = (change) => {
      if (!change) return '0%'
      const sign = change > 0 ? '+' : ''
      return `${sign}${change.toFixed(1)}%`
    }

    const getChangeClass = (change) => {
      if (change > 0) return 'positive'
      if (change < 0) return 'negative'
      return 'neutral'
    }

    const formatNumber = (num) => {
      return new Intl.NumberFormat('zh-CN').format(num)
    }

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('zh-CN')
    }

    const getStatusText = (status) => {
      const statusMap = {
        'COMPLETED': '已完成',
        'BOOKED': '已预约',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || status
    }

    // 导出报告
    const exportReport = async () => {
      try {
        const params = {
          ...getQueryParams(),
          format: 'excel',
          reportType: 'comprehensive'
        }
        
        const response = await doctorStatsApi.exportStatisticsReport(params)
        
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `医生统计报告_${new Date().toISOString().split('T')[0]}.xlsx`)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
        
      } catch (error) {
        console.error('导出报告失败:', error)
        alert('导出报告失败，请稍后重试')
      }
    }

    // 打开患者健康数据模态框
    const openPatientHealthModal = (patientId) => {
      selectedPatientId.value = patientId
      showPatientHealthModal.value = true
    }

    // 关闭患者健康数据模态框
    const closePatientHealthModal = () => {
      showPatientHealthModal.value = false
      selectedPatientId.value = null
    }

    // 组件挂载
    onMounted(() => {
      loadStatistics()
    })

    return {
      loading,
      selectedPeriod,
      customDateRange,
      statistics,
      recentRecords,
      showPatientHealthModal,
      selectedPatientId,
      appointmentTrendChart,
      ageDistributionChart,
      diseaseDistributionChart,
      workloadChart,
      loadStatistics,
      getDateRange,
      formatChange,
      getChangeClass,
      formatNumber,
      formatDate,
      getStatusText,
      exportReport,
      openPatientHealthModal,
      closePatientHealthModal
    }
  }
}
</script>

<style scoped>
.doctor-statistics {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-header h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 28px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.period-selector {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.date-range {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.export-btn {
  padding: 8px 16px;
  background: #4A90E2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.export-btn:hover {
  background: #357abd;
}

.loading {
  text-align: center;
  padding: 50px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.stats-overview {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.charts-section {
  margin-bottom: 30px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-container h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
}

.chart {
  width: 100%;
  height: 300px;
}

.data-tables {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-container h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #2c3e50;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.completed {
  background: #e8f5e8;
  color: #4caf50;
}

.status-badge.booked {
  background: #e3f2fd;
  color: #2196f3;
}

.status-badge.cancelled {
  background: #ffebee;
  color: #f44336;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

@media (max-width: 768px) {
  .chart-row {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
}
</style>
