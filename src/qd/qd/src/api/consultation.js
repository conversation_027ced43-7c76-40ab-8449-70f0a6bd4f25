import api from './index'

/**
 * 创建问诊会话（居民）
 * API测试报告确认的接口格式
 * @param {Object} data - 问诊数据
 * @param {number} data.doctorId - 医生ID
 * @returns {Promise} API响应
 */
export const createConsultation = (data) => {
  console.log('=== 创建问诊会话API调用 ===')
  console.log('请求数据:', data)
  return api.post('/consultations', data)
}

// 获取问诊列表
export const getConsultations = (params) => {
  return api.get('/consultations', { params })
}

// 发送消息
export const sendMessage = (consultationId, data) => {
  return api.post(`/consultations/${consultationId}/messages`, data)
}

// 获取消息历史记录
export const getMessages = (consultationId, params) => {
  return api.get(`/consultations/${consultationId}/messages`, { params })
}

// 完成问诊（医生）
export const completeConsultation = (consultationId) => {
  return api.put(`/consultations/${consultationId}/complete`)
}

// 获取医生列表（用于发起问诊）
export const getDoctorList = (params) => {
  console.log('=== 获取医生列表API调用 ===')
  console.log('请求参数:', params)

  // 根据后端AppointmentController，正确的API路径是 /api/appointments/doctors
  const searchParams = {
    page: params.page || 1,
    size: params.size || 100
  }

  // 如果有姓名搜索，使用name参数
  if (params.name) {
    searchParams.name = params.name
  }

  // 如果有科室筛选，使用departmentId参数（后端需要的是departmentId而不是department）
  if (params.departmentId) {
    searchParams.departmentId = params.departmentId
  }

  console.log('最终请求参数:', searchParams)
  return api.get('/appointments/doctors', { params: searchParams })
}
