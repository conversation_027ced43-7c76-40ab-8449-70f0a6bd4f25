<template>
  <div class="appointment-booking-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>预约挂号</h1>
      <div class="breadcrumb">
        <router-link to="/">首页</router-link>
        <span class="separator">></span>
        <span class="current">预约挂号</span>
      </div>
    </div>

    <!-- 预约流程指引 -->
    <div class="booking-guide">
      <div class="guide-steps">
        <div class="step-item">
          <div class="step-number">1</div>
          <div class="step-text">选择科室</div>
        </div>
        <div class="step-arrow">→</div>
        <div class="step-item">
          <div class="step-number">2</div>
          <div class="step-text">选择医生</div>
        </div>
        <div class="step-arrow">→</div>
        <div class="step-item">
          <div class="step-number">3</div>
          <div class="step-text">选择时间</div>
        </div>
        <div class="step-arrow">→</div>
        <div class="step-item">
          <div class="step-number">4</div>
          <div class="step-text">确认预约</div>
        </div>
      </div>
    </div>

    <!-- 快速预约入口 -->
    <div class="quick-booking">
      <h2>快速预约</h2>
      <div class="quick-options">
        <div class="quick-card" @click="quickBook('内科')">
          <div class="card-icon">🏥</div>
          <div class="card-title">内科</div>
          <div class="card-desc">感冒、发烧、咳嗽等</div>
          <div class="card-stats">
            <span>今日可约: 12个</span>
          </div>
        </div>

        <div class="quick-card" @click="quickBook('外科')">
          <div class="card-icon">🔬</div>
          <div class="card-title">外科</div>
          <div class="card-desc">外伤、手术等</div>
          <div class="card-stats">
            <span>今日可约: 8个</span>
          </div>
        </div>

        <div class="quick-card" @click="quickBook('儿科')">
          <div class="card-icon">👶</div>
          <div class="card-title">儿科</div>
          <div class="card-desc">儿童疾病诊疗</div>
          <div class="card-stats">
            <span>今日可约: 15个</span>
          </div>
        </div>

        <div class="quick-card" @click="quickBook('皮肤科')">
          <div class="card-icon">🧴</div>
          <div class="card-title">皮肤科</div>
          <div class="card-desc">皮肤病诊疗</div>
          <div class="card-stats">
            <span>今日可约: 6个</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门医生推荐 -->
    <div class="popular-doctors">
      <h2>热门医生</h2>
      <div class="doctors-grid">
        <div class="doctor-card" v-for="doctor in popularDoctors" :key="doctor.id">
          <div class="doctor-avatar">
            <img v-if="doctor.avatar" :src="doctor.avatar" :alt="doctor.name">
            <div v-else class="avatar-placeholder">{{ getInitials(doctor.name) }}</div>
          </div>
          <div class="doctor-info">
            <h3>{{ doctor.name }}</h3>
            <p class="doctor-title">{{ doctor.title }}</p>
            <p class="doctor-department">{{ doctor.department }}</p>
            <div class="doctor-stats">
              <span class="rating">⭐ {{ doctor.rating }}</span>
              <span class="patients">👥 {{ doctor.patientCount }}+</span>
            </div>
            <div class="doctor-specialty">
              <span class="specialty-tag" v-for="specialty in doctor.specialties" :key="specialty">
                {{ specialty }}
              </span>
            </div>
          </div>
          <div class="doctor-actions">
            <div class="available-slots">
              今日剩余: {{ doctor.availableSlots }}个
            </div>
            <button @click="bookDoctor(doctor)" class="book-btn">
              立即预约
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 预约须知 -->
    <div class="booking-notice">
      <h2>预约须知</h2>
      <div class="notice-content">
        <div class="notice-section">
          <h3>📋 预约流程</h3>
          <ul>
            <li>选择科室和医生</li>
            <li>选择合适的就诊时间</li>
            <li>填写就诊人信息</li>
            <li>确认预约并支付（如需要）</li>
          </ul>
        </div>

        <div class="notice-section">
          <h3>⏰ 预约时间</h3>
          <ul>
            <li>可预约未来7天内的号源</li>
            <li>当日预约需提前2小时</li>
            <li>节假日预约时间可能调整</li>
          </ul>
        </div>

        <div class="notice-section">
          <h3>❌ 取消规则</h3>
          <ul>
            <li>就诊前2小时可免费取消</li>
            <li>2小时内取消可能产生费用</li>
            <li>无故爽约将影响信用记录</li>
          </ul>
        </div>

        <div class="notice-section">
          <h3>📱 就诊提醒</h3>
          <ul>
            <li>预约成功后将发送确认短信</li>
            <li>就诊前30分钟发送提醒</li>
            <li>请携带身份证和相关病历</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 联系我们 -->
    <div class="contact-section">
      <h2>需要帮助？</h2>
      <div class="contact-options">
        <div class="contact-item">
          <div class="contact-icon">📞</div>
          <div class="contact-info">
            <h3>电话咨询</h3>
            <p>400-123-4567</p>
            <span>工作时间: 8:00-18:00</span>
          </div>
        </div>

        <div class="contact-item">
          <div class="contact-icon">💬</div>
          <div class="contact-info">
            <h3>在线客服</h3>
            <p>点击咨询</p>
            <span>24小时在线服务</span>
          </div>
        </div>

        <div class="contact-item">
          <div class="contact-icon">📍</div>
          <div class="contact-info">
            <h3>医院地址</h3>
            <p>北京市朝阳区健康路123号</p>
            <span>地铁1号线健康站A出口</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'AppointmentBooking',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const popularDoctors = ref([
      {
        id: 1,
        name: '王健康',
        title: '主任医师',
        department: '内科',
        rating: 4.9,
        patientCount: 1200,
        specialties: ['心血管', '高血压'],
        availableSlots: 5,
        avatar: null
      },
      {
        id: 2,
        name: '刘平安',
        title: '副主任医师',
        department: '外科',
        rating: 4.8,
        patientCount: 980,
        specialties: ['普外科', '微创手术'],
        availableSlots: 3,
        avatar: null
      },
      {
        id: 3,
        name: '陈爱婴',
        title: '主治医师',
        department: '儿科',
        rating: 4.9,
        patientCount: 1500,
        specialties: ['儿童呼吸', '新生儿'],
        availableSlots: 8,
        avatar: null
      }
    ])

    // 方法
    const quickBook = (department) => {
      // 跳转到预约页面，并传递科室参数
      router.push({
        name: 'patient-appointments',
        query: { department }
      })
    }

    const bookDoctor = (doctor) => {
      // 跳转到预约页面，并传递医生参数
      router.push({
        name: 'patient-appointments',
        query: { doctorId: doctor.userId || doctor.id }
      })
    }

    const getInitials = (name) => {
      if (!name) return '?'
      return name.charAt(0).toUpperCase()
    }

    // 组件挂载
    onMounted(() => {
      // 可以在这里加载更多数据
    })

    return {
      popularDoctors,
      quickBook,
      bookDoctor,
      getInitials
    }
  }
}
</script>

<style scoped>
.appointment-booking-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 32px;
  font-weight: 700;
}

.breadcrumb {
  color: #6c757d;
  font-size: 14px;
}

.breadcrumb a {
  color: #4A90E2;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.separator {
  margin: 0 8px;
}

.current {
  color: #2c3e50;
  font-weight: 500;
}

.booking-guide {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.guide-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4A90E2, #357abd);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
}

.step-text {
  color: #2c3e50;
  font-size: 14px;
  font-weight: 500;
}

.step-arrow {
  color: #4A90E2;
  font-size: 20px;
  font-weight: bold;
}

.quick-booking {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.quick-booking h2 {
  margin: 0 0 25px 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.quick-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.quick-card {
  padding: 25px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-card:hover {
  border-color: #4A90E2;
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.15);
}

.card-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.card-title {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.card-desc {
  color: #6c757d;
  font-size: 14px;
  margin-bottom: 15px;
}

.card-stats {
  color: #28a745;
  font-size: 12px;
  font-weight: 500;
}

.popular-doctors {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.popular-doctors h2 {
  margin: 0 0 25px 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.doctors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.doctor-card {
  display: flex;
  gap: 20px;
  padding: 25px;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.doctor-card:hover {
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.doctor-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.doctor-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  font-size: 32px;
  font-weight: bold;
  color: #6c757d;
}

.doctor-info {
  flex: 1;
}

.doctor-info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.doctor-title {
  margin: 0 0 4px 0;
  color: #4A90E2;
  font-weight: 500;
}

.doctor-department {
  margin: 0 0 12px 0;
  color: #6c757d;
  font-size: 14px;
}

.doctor-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 12px;
}

.rating, .patients {
  font-size: 12px;
  color: #6c757d;
}

.doctor-specialty {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.specialty-tag {
  padding: 4px 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  font-size: 12px;
  color: #495057;
}

.doctor-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;
}

.available-slots {
  color: #28a745;
  font-size: 12px;
  font-weight: 500;
}

.book-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #4A90E2, #357abd);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.book-btn:hover {
  background: linear-gradient(135deg, #357abd, #2c5aa0);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.booking-notice {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.booking-notice h2 {
  margin: 0 0 25px 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.notice-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.notice-section h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.notice-section ul {
  margin: 0;
  padding-left: 20px;
  color: #495057;
}

.notice-section li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.contact-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.contact-section h2 {
  margin: 0 0 25px 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
  text-align: center;
}

.contact-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 25px;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.contact-item:hover {
  border-color: #4A90E2;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.contact-icon {
  font-size: 40px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 50%;
}

.contact-info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.contact-info p {
  margin: 0 0 4px 0;
  color: #4A90E2;
  font-weight: 500;
}

.contact-info span {
  color: #6c757d;
  font-size: 12px;
}

@media (max-width: 768px) {
  .guide-steps {
    flex-direction: column;
    gap: 15px;
  }
  
  .step-arrow {
    transform: rotate(90deg);
  }
  
  .quick-options {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .doctors-grid {
    grid-template-columns: 1fr;
  }
  
  .doctor-card {
    flex-direction: column;
    text-align: center;
  }
  
  .notice-content {
    grid-template-columns: 1fr;
  }
  
  .contact-options {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .quick-options {
    grid-template-columns: 1fr;
  }
}
</style>
