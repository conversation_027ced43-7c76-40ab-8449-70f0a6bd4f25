<template>
  <div class="profile-form">
    <div class="form-header">
      <h3>{{ isEdit ? '编辑健康档案' : '创建健康档案' }}</h3>
      <p class="form-description">请填写完整的健康档案信息，以便为您提供更好的健康服务</p>
    </div>

    <form @submit.prevent="handleSubmit" class="form-content">
      <!-- 头像上传 -->
      <div class="form-group avatar-group">
        <label class="form-label">头像</label>
        <div class="avatar-upload">
          <div class="avatar-preview" @click="triggerFileInput">
            <img v-if="formData.avatarUrl" :src="formData.avatarUrl" alt="头像" />
            <div v-else class="avatar-placeholder">
              <div class="avatar-icon">📷</div>
              <span>点击上传头像</span>
            </div>
          </div>
          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            @change="handleAvatarUpload"
            style="display: none"
          />
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="form-row">
        <div class="form-group">
          <label for="name" class="form-label required">姓名</label>
          <input
            id="name"
            v-model="formData.profileOwnerName"
            type="text"
            placeholder="请输入真实姓名"
            :class="{ 'error': errors.profileOwnerName }"
            @blur="validateField('profileOwnerName')"
          />
          <span v-if="errors.profileOwnerName" class="error-message">
            {{ errors.profileOwnerName }}
          </span>
        </div>

        <div class="form-group">
          <label for="gender" class="form-label required">性别</label>
          <select
            id="gender"
            v-model="formData.gender"
            :class="{ 'error': errors.gender }"
            @change="validateField('gender')"
          >
            <option value="">请选择性别</option>
            <option value="MALE">男</option>
            <option value="FEMALE">女</option>
            <option value="UNKNOWN">其他</option>
          </select>
          <span v-if="errors.gender" class="error-message">
            {{ errors.gender }}
          </span>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="birthDate" class="form-label required">出生日期</label>
          <input
            id="birthDate"
            v-model="formData.birthDate"
            type="date"
            :max="maxDate"
            :class="{ 'error': errors.birthDate }"
            @change="validateField('birthDate')"
          />
          <span v-if="errors.birthDate" class="error-message">
            {{ errors.birthDate }}
          </span>
        </div>

        <div class="form-group">
          <label for="idCard" class="form-label required">身份证号</label>
          <input
            id="idCard"
            v-model="formData.idCard"
            type="text"
            placeholder="请输入18位身份证号"
            maxlength="18"
            :class="{ 'error': errors.idCard }"
            @blur="validateIdCard"
          />
          <span v-if="errors.idCard" class="error-message">
            {{ errors.idCard }}
          </span>
        </div>
      </div>

      <!-- 病史信息 -->
      <div class="form-group">
        <label for="medicalHistory" class="form-label">病史信息</label>
        <textarea
          id="medicalHistory"
          v-model="formData.medicalHistory"
          placeholder="请输入既往病史、过敏史、手术史等信息（可选）"
          rows="4"
          :class="{ 'error': errors.medicalHistory }"
        ></textarea>
        <span v-if="errors.medicalHistory" class="error-message">
          {{ errors.medicalHistory }}
        </span>
      </div>

      <!-- 表单操作 -->
      <div class="form-actions">
        <button type="button" @click="handleCancel" class="btn-cancel">
          取消
        </button>
        <button type="submit" :disabled="isSubmitting" class="btn-submit">
          {{ isSubmitting ? '保存中...' : (isEdit ? '更新档案' : '创建档案') }}
        </button>
      </div>
    </form>

    <!-- 错误提示 -->
    <div v-if="submitError" class="error-alert">
      {{ submitError }}
    </div>

    <!-- 成功提示 -->
    <div v-if="submitSuccess" class="success-alert">
      {{ isEdit ? '档案更新成功！' : '档案创建成功！' }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { createHealthProfile, updateHealthProfile, uploadAvatar, validateIdCard as apiValidateIdCard } from '@/api/health'

// Props
const props = defineProps({
  profile: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['success', 'cancel'])

// 响应式数据
const fileInput = ref(null)
const isSubmitting = ref(false)
const submitError = ref('')
const submitSuccess = ref(false)

const formData = reactive({
  profileOwnerName: '',
  gender: '',
  birthDate: '',
  idCard: '',
  avatarUrl: '',
  medicalHistory: ''
})

const errors = reactive({
  profileOwnerName: '',
  gender: '',
  birthDate: '',
  idCard: '',
  medicalHistory: ''
})

// 计算属性
const maxDate = computed(() => {
  const today = new Date()
  return today.toISOString().split('T')[0]
})

// 监听props变化
watch(() => props.profile, (newProfile) => {
  if (newProfile) {
    Object.assign(formData, {
      profileOwnerName: newProfile.profileOwnerName || '',
      gender: newProfile.gender || '',
      birthDate: newProfile.birthDate || '',
      idCard: newProfile.idCard || '',
      avatarUrl: newProfile.avatarUrl || '',
      medicalHistory: newProfile.medicalHistory || ''
    })
  }
}, { immediate: true })

// 方法
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleAvatarUpload = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    errors.avatar_url = '请选择图片文件'
    return
  }

  // 验证文件大小 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    errors.avatar_url = '图片大小不能超过5MB'
    return
  }

  try {
    const response = await uploadAvatar(file)
    if (response.data.code === 200) {
      formData.avatarUrl = response.data.data.url
      errors.avatarUrl = ''
    } else {
      errors.avatarUrl = response.data.message || '头像上传失败'
    }
  } catch (error) {
    console.error('头像上传失败:', error)
    errors.avatarUrl = '头像上传失败，请重试'
  }
}

const validateField = (field) => {
  errors[field] = ''

  switch (field) {
    case 'profileOwnerName':
      if (!formData.profileOwnerName.trim()) {
        errors[field] = '请输入姓名'
      } else if (formData.profileOwnerName.length < 2) {
        errors[field] = '姓名至少2个字符'
      }
      break

    case 'gender':
      if (!formData.gender) {
        errors[field] = '请选择性别'
      }
      break

    case 'birthDate':
      if (!formData.birthDate) {
        errors[field] = '请选择出生日期'
      } else {
        const birthDate = new Date(formData.birthDate)
        const today = new Date()
        if (birthDate > today) {
          errors[field] = '出生日期不能晚于今天'
        }
      }
      break

    case 'idCard':
      if (!formData.idCard.trim()) {
        errors[field] = '请输入身份证号'
      } else if (!/^\d{17}[\dXx]$/.test(formData.idCard)) {
        errors[field] = '请输入正确的18位身份证号'
      }
      break
  }
}

const validateIdCard = async () => {
  validateField('idCard')

  if (!errors.idCard && formData.idCard) {
    try {
      const response = await apiValidateIdCard(formData.idCard)
      if (response.data.code !== 200) {
        errors.idCard = response.data.message || '身份证号已存在'
      }
    } catch (error) {
      console.error('身份证验证失败:', error)
      // 验证失败不阻止用户继续操作
    }
  }
}

const validateForm = () => {
  validateField('profileOwnerName')
  validateField('gender')
  validateField('birthDate')
  validateField('idCard')

  return !Object.values(errors).some(error => error)
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true
  submitError.value = ''
  submitSuccess.value = false

  try {
    let response
    if (props.isEdit && props.profile?.id) {
      response = await updateHealthProfile(props.profile.id, formData)
    } else {
      response = await createHealthProfile(formData)
    }

    if (response.data.code === 200) {
      submitSuccess.value = true
      setTimeout(() => {
        emit('success', response.data.data)
      }, 1000)
    } else {
      submitError.value = response.data.message || '操作失败'
    }
  } catch (error) {
    console.error('提交失败:', error)
    submitError.value = error.response?.data?.message || '网络错误，请重试'
  } finally {
    isSubmitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.profile-form {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-header {
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.form-header h3 {
  color: #262626;
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.form-description {
  color: #8c8c8c;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.form-content {
  padding: 28px;
}

.form-group {
  margin-bottom: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.form-label {
  display: block;
  color: #262626;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
}

.form-label.required::after {
  content: ' *';
  color: #ff4d4f;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

/* 头像上传 */
.avatar-group {
  text-align: center;
  margin-bottom: 32px;
}

.avatar-upload {
  display: inline-block;
}

.avatar-preview {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 2px dashed #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  background: #fafafa;
}

.avatar-preview:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatar-placeholder {
  text-align: center;
  color: #8c8c8c;
}

.avatar-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.avatar-placeholder span {
  font-size: 12px;
  display: block;
}

/* 表单操作 */
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.btn-cancel,
.btn-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-cancel {
  background: #f5f5f5;
  color: #595959;
  border: 1px solid #d9d9d9;
}

.btn-cancel:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.btn-submit {
  background: #1890ff;
  color: white;
  min-width: 120px;
}

.btn-submit:hover:not(:disabled) {
  background: #096dd9;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.btn-submit:disabled {
  background: #d9d9d9;
  color: #8c8c8c;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 提示信息 */
.error-alert,
.success-alert {
  margin: 16px 28px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.error-alert {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.success-alert {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-content {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .btn-cancel,
  .btn-submit {
    width: 100%;
  }

  .avatar-preview {
    width: 100px;
    height: 100px;
  }
}
</style>
