/**
 * 日期处理工具函数
 */

/**
 * 格式化日期为 YYYY-MM-DD 格式
 * @param {Date|string} date - 日期对象或字符串
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  
  return `${year}-${month}-${day}`
}

/**
 * 格式化时间为 HH:mm:ss 格式
 * @param {string} time - 时间字符串 (HH:mm 或 HH:mm:ss)
 * @returns {string} 格式化后的时间字符串
 */
export const formatTime = (time) => {
  if (!time) return ''
  
  // 如果已经是 HH:mm:ss 格式，直接返回
  if (time.length === 8 && time.includes(':')) {
    return time
  }
  
  // 如果是 HH:mm 格式，添加秒数
  if (time.length === 5 && time.includes(':')) {
    return `${time}:00`
  }
  
  return time
}

/**
 * 格式化时间为显示格式 HH:mm
 * @param {string} time - 时间字符串 (HH:mm:ss)
 * @returns {string} 显示格式的时间字符串
 */
export const formatTimeDisplay = (time) => {
  if (!time) return ''
  
  // 如果是 HH:mm:ss 格式，去掉秒数
  if (time.length === 8 && time.includes(':')) {
    return time.substring(0, 5)
  }
  
  return time
}

/**
 * 获取今天的日期字符串
 * @returns {string} 今天的日期 (YYYY-MM-DD)
 */
export const getToday = () => {
  return formatDate(new Date())
}

/**
 * 获取明天的日期字符串
 * @returns {string} 明天的日期 (YYYY-MM-DD)
 */
export const getTomorrow = () => {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return formatDate(tomorrow)
}

/**
 * 获取指定日期的星期几
 * @param {Date|string} date - 日期
 * @returns {number} 星期几 (0=周日, 1=周一, ..., 6=周六)
 */
export const getWeekday = (date) => {
  const d = new Date(date)
  return d.getDay()
}

/**
 * 获取星期几的中文名称
 * @param {number} weekday - 星期几 (0=周日, 1=周一, ..., 6=周六)
 * @returns {string} 中文星期名称
 */
export const getWeekdayName = (weekday) => {
  const names = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return names[weekday] || ''
}

/**
 * 获取日期的中文显示格式
 * @param {Date|string} date - 日期
 * @returns {string} 中文日期格式 (如: 2025年6月14日 周六)
 */
export const getDateDisplayName = (date) => {
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  const weekday = getWeekdayName(d.getDay())
  
  return `${year}年${month}月${day}日 ${weekday}`
}

/**
 * 检查日期是否是今天
 * @param {Date|string} date - 日期
 * @returns {boolean} 是否是今天
 */
export const isToday = (date) => {
  return formatDate(date) === getToday()
}

/**
 * 检查日期是否是明天
 * @param {Date|string} date - 日期
 * @returns {boolean} 是否是明天
 */
export const isTomorrow = (date) => {
  return formatDate(date) === getTomorrow()
}

/**
 * 检查日期是否是过去的日期
 * @param {Date|string} date - 日期
 * @returns {boolean} 是否是过去的日期
 */
export const isPastDate = (date) => {
  const today = getToday()
  const targetDate = formatDate(date)
  return targetDate < today
}

/**
 * 检查日期是否是未来的日期
 * @param {Date|string} date - 日期
 * @returns {boolean} 是否是未来的日期
 */
export const isFutureDate = (date) => {
  const today = getToday()
  const targetDate = formatDate(date)
  return targetDate > today
}

/**
 * 获取两个日期之间的天数差
 * @param {Date|string} startDate - 开始日期
 * @param {Date|string} endDate - 结束日期
 * @returns {number} 天数差
 */
export const getDaysDifference = (startDate, endDate) => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const diffTime = end.getTime() - start.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

/**
 * 获取指定日期范围内的所有日期
 * @param {Date|string} startDate - 开始日期
 * @param {Date|string} endDate - 结束日期
 * @returns {Array} 日期数组
 */
export const getDateRange = (startDate, endDate) => {
  const dates = []
  const start = new Date(startDate)
  const end = new Date(endDate)
  
  for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
    dates.push(formatDate(new Date(d)))
  }
  
  return dates
}

/**
 * 获取本周的开始和结束日期
 * @param {Date|string} date - 参考日期（默认为今天）
 * @returns {Object} { startDate, endDate }
 */
export const getWeekRange = (date = new Date()) => {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() - day + (day === 0 ? -6 : 1) // 调整为周一开始
  
  const startDate = new Date(d.setDate(diff))
  const endDate = new Date(startDate)
  endDate.setDate(startDate.getDate() + 6)
  
  return {
    startDate: formatDate(startDate),
    endDate: formatDate(endDate)
  }
}

/**
 * 获取本月的开始和结束日期
 * @param {Date|string} date - 参考日期（默认为今天）
 * @returns {Object} { startDate, endDate }
 */
export const getMonthRange = (date = new Date()) => {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = d.getMonth()
  
  const startDate = new Date(year, month, 1)
  const endDate = new Date(year, month + 1, 0)
  
  return {
    startDate: formatDate(startDate),
    endDate: formatDate(endDate)
  }
}

/**
 * 比较两个时间的大小
 * @param {string} time1 - 时间1 (HH:mm 或 HH:mm:ss)
 * @param {string} time2 - 时间2 (HH:mm 或 HH:mm:ss)
 * @returns {number} -1: time1 < time2, 0: time1 = time2, 1: time1 > time2
 */
export const compareTime = (time1, time2) => {
  const t1 = formatTime(time1)
  const t2 = formatTime(time2)
  
  if (t1 < t2) return -1
  if (t1 > t2) return 1
  return 0
}

/**
 * 检查时间是否有效
 * @param {string} time - 时间字符串
 * @returns {boolean} 是否有效
 */
export const isValidTime = (time) => {
  if (!time) return false
  
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/
  return timeRegex.test(time)
}

/**
 * 检查日期是否有效
 * @param {string} date - 日期字符串
 * @returns {boolean} 是否有效
 */
export const isValidDate = (date) => {
  if (!date) return false
  
  const d = new Date(date)
  return !isNaN(d.getTime()) && formatDate(d) === date
}

/**
 * 获取相对日期描述
 * @param {Date|string} date - 日期
 * @returns {string} 相对日期描述 (如: 今天, 明天, 昨天, 3天后)
 */
export const getRelativeDateDescription = (date) => {
  const targetDate = formatDate(date)
  const today = getToday()
  const tomorrow = getTomorrow()
  
  if (targetDate === today) return '今天'
  if (targetDate === tomorrow) return '明天'
  
  const yesterday = formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000))
  if (targetDate === yesterday) return '昨天'
  
  const daysDiff = getDaysDifference(today, targetDate)
  
  if (daysDiff > 0) {
    return `${daysDiff}天后`
  } else {
    return `${Math.abs(daysDiff)}天前`
  }
}
