import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as userApi from '@/api/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || 'null'))
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userRole = computed(() => userInfo.value?.role || '')
  const isResident = computed(() => {
    if (!userInfo.value) return false
    return userInfo.value.isDoctor === false && userInfo.value.isAdmin === false
  })
  const isDoctor = computed(() => {
    if (!userInfo.value) return false
    return userInfo.value.isDoctor === true
  })
  const isAdmin = computed(() => {
    if (!userInfo.value) return false
    return userInfo.value.isAdmin === true
  })

  // 登录
  const login = async (loginData) => {
    try {
      isLoading.value = true
      const response = await userApi.login(loginData)

      if (response.data.code === 200) {
        const { token: newToken, userInfo: user } = response.data.data

        // 保存token和用户信息
        token.value = newToken
        userInfo.value = user

        // 持久化到localStorage
        localStorage.setItem('token', newToken)
        localStorage.setItem('userInfo', JSON.stringify(user))

        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('登录失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || '登录失败，请稍后重试'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (registerData) => {
    try {
      isLoading.value = true
      const response = await userApi.register(registerData)

      if (response.data.code === 200) {
        return { success: true, data: response.data }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error) {
      console.error('注册失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || '注册失败，请稍后重试'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await userApi.getUserInfo()
      if (response.data.code === 200) {
        userInfo.value = response.data.data
        localStorage.setItem('userInfo', JSON.stringify(response.data.data))
        return { success: true, data: response.data.data }
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return { success: false, message: '获取用户信息失败' }
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出接口
      await userApi.logout()
    } catch (error) {
      console.error('登出接口调用失败:', error)
    } finally {
      // 无论接口是否成功，都清除本地数据
      token.value = ''
      userInfo.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
    }
  }

  // 检查token有效性
  const checkTokenValidity = async () => {
    if (!token.value) return false
    
    try {
      const result = await fetchUserInfo()
      return result.success
    } catch (error) {
      // token无效，清除本地数据
      logout()
      return false
    }
  }

  return {
    // 状态
    token,
    userInfo,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    userRole,
    isResident,
    isDoctor,
    isAdmin,
    
    // 方法
    login,
    register,
    logout,
    fetchUserInfo,
    checkTokenValidity
  }
})
