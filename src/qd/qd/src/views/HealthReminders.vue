<template>
  <div class="health-reminders-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>健康提醒</h1>
        <p class="page-description">智能健康提醒管理，让健康生活更有规律</p>
      </div>
      <div class="header-actions">
        <button @click="showAddForm" class="btn-primary">
          <span class="btn-icon">⏰</span>
          添加提醒
        </button>
      </div>
    </div>

    <!-- 今日提醒区域 -->
    <div v-if="todayReminders.length > 0" class="today-section">
      <div class="section-header">
        <h3>📅 今日提醒</h3>
        <span class="reminder-count">{{ todayReminders.length }} 个提醒</span>
      </div>
      <div class="today-reminders">
        <div 
          v-for="reminder in todayReminders" 
          :key="reminder.id" 
          class="today-reminder-card"
          :class="{ 'completed': reminder.completed }"
        >
          <div class="reminder-icon">
            {{ getReminderIcon(reminder.reminderType) }}
          </div>
          <div class="reminder-info">
            <h4>{{ reminder.title }}</h4>
            <p>{{ reminder.content }}</p>
            <div class="reminder-time">
              {{ formatTime(reminder.nextReminderTime) }}
            </div>
          </div>
          <div class="reminder-actions">
            <button 
              v-if="!reminder.completed"
              @click="completeReminder(reminder)" 
              class="complete-btn"
              title="标记完成"
            >
              ✓
            </button>
            <span v-else class="completed-mark">已完成</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 所有提醒列表 -->
    <div class="reminders-section">
      <div class="section-header">
        <h3>📋 所有提醒</h3>
        <div class="header-controls">
          <select v-model="filterType" @change="loadReminders(1)" class="filter-select">
            <option value="">全部类型</option>
            <option value="MEDICATION">💊 用药提醒</option>
            <option value="CHECKUP">🏥 体检提醒</option>
            <option value="EXERCISE">🏃 运动提醒</option>
            <option value="DIET">🥗 饮食提醒</option>
            <option value="MEASUREMENT">📊 测量提醒</option>
          </select>
          <select v-model="filterActive" @change="loadReminders(1)" class="filter-select">
            <option value="">全部状态</option>
            <option value="true">已启用</option>
            <option value="false">已禁用</option>
          </select>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner">⏳</div>
        <p>加载提醒列表...</p>
      </div>

      <!-- 提醒列表 -->
      <div v-else-if="reminders.length > 0" class="reminders-list">
        <div 
          v-for="reminder in reminders" 
          :key="reminder.id" 
          class="reminder-item"
          :class="{ 'inactive': !reminder.isActive }"
        >
          <div class="reminder-icon">
            {{ getReminderIcon(reminder.reminderType) }}
          </div>
          <div class="reminder-content">
            <div class="reminder-header">
              <h4>{{ reminder.title }}</h4>
              <div class="reminder-badges">
                <span class="type-badge" :class="reminder.reminderType.toLowerCase()">
                  {{ getReminderTypeLabel(reminder.reminderType) }}
                </span>
                <span class="frequency-badge">
                  {{ getFrequencyLabel(reminder.frequencyType, reminder.frequencyValue) }}
                </span>
                <span v-if="!reminder.isActive" class="status-badge inactive">已禁用</span>
              </div>
            </div>
            <p class="reminder-description">{{ reminder.content }}</p>
            <div class="reminder-meta">
              <span class="next-time">
                下次提醒: {{ formatDateTime(reminder.nextReminderTime) }}
              </span>
              <span class="created-time">
                创建于: {{ formatDate(reminder.createdAt) }}
              </span>
            </div>
          </div>
          <div class="reminder-actions">
            <button @click="editReminder(reminder)" class="action-btn edit" title="编辑">
              ✏️
            </button>
            <button 
              @click="toggleReminderStatus(reminder)" 
              class="action-btn toggle"
              :title="reminder.isActive ? '禁用' : '启用'"
            >
              {{ reminder.isActive ? '⏸️' : '▶️' }}
            </button>
            <button @click="deleteReminder(reminder)" class="action-btn delete" title="删除">
              🗑️
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <div class="empty-icon">⏰</div>
        <h3>还没有健康提醒</h3>
        <p>创建您的第一个健康提醒，让生活更有规律</p>
        <button @click="showAddForm" class="btn-primary">
          创建提醒
        </button>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.totalPages > 1" class="pagination">
        <button 
          @click="changePage(pagination.currentPage - 1)"
          :disabled="pagination.currentPage <= 1"
          class="page-btn"
        >
          上一页
        </button>
        <span class="page-info">
          第 {{ pagination.currentPage }} 页，共 {{ pagination.totalPages }} 页
        </span>
        <button 
          @click="changePage(pagination.currentPage + 1)"
          :disabled="pagination.currentPage >= pagination.totalPages"
          class="page-btn"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 成功消息 -->
    <div v-if="successMessage" class="success-message">
      {{ successMessage }}
    </div>

    <!-- 添加/编辑提醒弹窗 -->
    <div v-if="showForm" class="form-modal">
      <div class="modal-overlay" @click="hideForm"></div>
      <div class="modal-content">
        <ReminderForm
          :reminder="selectedReminder"
          :is-edit-mode="isEditMode"
          @success="handleFormSuccess"
          @cancel="hideForm"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { 
  getHealthReminders, 
  getTodayReminders, 
  completeHealthReminder, 
  deleteHealthReminder,
  updateHealthReminder
} from '@/api/reminders'
import ReminderForm from '@/components/ReminderForm.vue'

// 响应式数据
const reminders = ref([])
const todayReminders = ref([])
const loading = ref(false)
const showForm = ref(false)
const selectedReminder = ref(null)
const isEditMode = ref(false)
const successMessage = ref('')

// 筛选条件
const filterType = ref('')
const filterActive = ref('')

// 分页
const pagination = ref({
  currentPage: 1,
  totalPages: 1,
  totalRecords: 0,
  pageSize: 10
})

// 提醒类型配置
const REMINDER_TYPES = {
  MEDICATION: { icon: '💊', label: '用药提醒' },
  CHECKUP: { icon: '🏥', label: '体检提醒' },
  EXERCISE: { icon: '🏃', label: '运动提醒' },
  DIET: { icon: '🥗', label: '饮食提醒' },
  MEASUREMENT: { icon: '📊', label: '测量提醒' }
}

// 频率类型配置
const FREQUENCY_TYPES = {
  DAILY: '每日',
  WEEKLY: '每周',
  MONTHLY: '每月',
  CUSTOM: '自定义'
}

// 工具函数
const getReminderIcon = (type) => {
  return REMINDER_TYPES[type]?.icon || '⏰'
}

const getReminderTypeLabel = (type) => {
  return REMINDER_TYPES[type]?.label || type
}

const getFrequencyLabel = (type, value) => {
  const baseLabel = FREQUENCY_TYPES[type] || type
  if (type === 'WEEKLY' && value.includes(',')) {
    const [day] = value.split(',')
    const dayNames = {
      'MONDAY': '周一', 'TUESDAY': '周二', 'WEDNESDAY': '周三',
      'THURSDAY': '周四', 'FRIDAY': '周五', 'SATURDAY': '周六', 'SUNDAY': '周日'
    }
    return `${dayNames[day] || day}`
  }
  return baseLabel
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

const formatTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 数据加载函数
const loadReminders = async (page = 1) => {
  loading.value = true
  try {
    const params = {
      page: page,
      size: pagination.value.pageSize
    }

    if (filterType.value) {
      params.reminderType = filterType.value
    }
    if (filterActive.value !== '') {
      params.isActive = filterActive.value === 'true'
    }

    const response = await getHealthReminders(params)
    if (response.data.code === 200) {
      const data = response.data.data
      reminders.value = data.reminders || []

      if (data.pagination) {
        pagination.value.currentPage = data.pagination.currentPage || page
        pagination.value.totalRecords = data.pagination.totalRecords || 0
        pagination.value.totalPages = data.pagination.totalPages || 0
      }
    } else {
      console.error('获取提醒列表失败:', response.data.message)
    }
  } catch (error) {
    console.error('获取提醒列表失败:', error)
  } finally {
    loading.value = false
  }
}

const loadTodayReminders = async () => {
  try {
    const response = await getTodayReminders()
    if (response.data.code === 200) {
      todayReminders.value = response.data.data || []
    } else {
      console.error('获取今日提醒失败:', response.data.message)
    }
  } catch (error) {
    console.error('获取今日提醒失败:', error)
  }
}

// 提醒操作函数
const completeReminder = async (reminder) => {
  try {
    const response = await completeHealthReminder(reminder.id)
    if (response.data.code === 200) {
      showSuccessMessage('提醒已标记完成！')
      // 标记为已完成（本地状态）
      reminder.completed = true
      // 重新加载数据
      await loadReminders(pagination.value.currentPage)
      await loadTodayReminders()
    } else {
      alert('标记完成失败：' + response.data.message)
    }
  } catch (error) {
    console.error('标记完成失败:', error)
    alert('标记完成失败，请重试')
  }
}

const toggleReminderStatus = async (reminder) => {
  try {
    const updatedReminder = {
      ...reminder,
      isActive: !reminder.isActive
    }

    const response = await updateHealthReminder(reminder.id, updatedReminder)
    if (response.data.code === 200) {
      showSuccessMessage(reminder.isActive ? '提醒已禁用' : '提醒已启用')
      await loadReminders(pagination.value.currentPage)
    } else {
      alert('状态更新失败：' + response.data.message)
    }
  } catch (error) {
    console.error('状态更新失败:', error)
    alert('状态更新失败，请重试')
  }
}

const deleteReminder = async (reminder) => {
  if (!confirm(`确定要删除提醒"${reminder.title}"吗？`)) {
    return
  }

  try {
    const response = await deleteHealthReminder(reminder.id)
    if (response.data.code === 200) {
      showSuccessMessage('提醒删除成功！')
      await loadReminders(pagination.value.currentPage)
      await loadTodayReminders()
    } else {
      alert('删除失败：' + response.data.message)
    }
  } catch (error) {
    console.error('删除提醒失败:', error)
    alert('删除失败，请重试')
  }
}

// 表单操作函数
const showAddForm = () => {
  selectedReminder.value = null
  isEditMode.value = false
  showForm.value = true
}

const editReminder = (reminder) => {
  selectedReminder.value = reminder
  isEditMode.value = true
  showForm.value = true
}

const hideForm = () => {
  showForm.value = false
  selectedReminder.value = null
}

const handleFormSuccess = async () => {
  hideForm()
  showSuccessMessage(isEditMode.value ? '提醒更新成功！' : '提醒创建成功！')
  await loadReminders(pagination.value.currentPage)
  await loadTodayReminders()
}

// 分页函数
const changePage = (page) => {
  if (page >= 1 && page <= pagination.value.totalPages) {
    loadReminders(page)
  }
}

// 成功消息
const showSuccessMessage = (message) => {
  successMessage.value = message
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
}

// 生命周期
onMounted(() => {
  loadReminders()
  loadTodayReminders()
})
</script>

<style scoped>
.health-reminders-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f0f0;
}

.header-content h1 {
  color: #262626;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.page-description {
  color: #8c8c8c;
  font-size: 14px;
  margin: 0;
}

.header-actions .btn-primary {
  background: #1890ff;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions .btn-primary:hover {
  background: #096dd9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.btn-icon {
  font-size: 16px;
}

/* 今日提醒区域 */
.today-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  color: white;
}

.today-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.today-section .section-header h3 {
  color: white;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.today-section .reminder-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.today-reminders {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.today-reminder-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
}

.today-reminder-card:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.today-reminder-card.completed {
  opacity: 0.7;
  background: rgba(255, 255, 255, 0.1);
}

.today-reminder-card .reminder-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.today-reminder-card .reminder-info {
  flex: 1;
}

.today-reminder-card .reminder-info h4 {
  color: white;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.today-reminder-card .reminder-info p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  margin: 0 0 4px 0;
}

.today-reminder-card .reminder-time {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: 600;
}

.today-reminder-card .reminder-actions {
  display: flex;
  align-items: center;
}

.complete-btn {
  width: 32px;
  height: 32px;
  background: #52c41a;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.2s ease;
}

.complete-btn:hover {
  background: #389e0d;
  transform: scale(1.1);
}

.completed-mark {
  color: #52c41a;
  font-size: 12px;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
}

/* 提醒列表区域 */
.reminders-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.section-header h3 {
  color: #262626;
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}

.header-controls {
  display: flex;
  gap: 12px;
}

.filter-select {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 13px;
  color: #262626;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-select:hover {
  border-color: #1890ff;
}

.filter-select:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  font-size: 32px;
  margin-bottom: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 提醒列表 */
.reminders-list {
  padding: 0;
}

.reminder-item {
  display: flex;
  align-items: center;
  padding: 20px 28px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.reminder-item:hover {
  background: #fafafa;
}

.reminder-item:last-child {
  border-bottom: none;
}

.reminder-item.inactive {
  opacity: 0.6;
  background: #f9f9f9;
}

.reminder-item .reminder-icon {
  font-size: 32px;
  margin-right: 16px;
  width: 48px;
  text-align: center;
}

.reminder-content {
  flex: 1;
}

.reminder-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.reminder-header h4 {
  color: #262626;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.reminder-badges {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.type-badge, .frequency-badge, .status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.type-badge.medication { background: #e6f7ff; color: #1890ff; }
.type-badge.checkup { background: #f6ffed; color: #52c41a; }
.type-badge.exercise { background: #fff2e8; color: #fa8c16; }
.type-badge.diet { background: #f9f0ff; color: #722ed1; }
.type-badge.measurement { background: #fff1f0; color: #f5222d; }

.frequency-badge {
  background: #f0f0f0;
  color: #595959;
}

.status-badge.inactive {
  background: #fff2f0;
  color: #ff4d4f;
}

.reminder-description {
  color: #595959;
  font-size: 14px;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.reminder-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #8c8c8c;
}

.reminder-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action-btn.edit {
  background: #e6f7ff;
  color: #1890ff;
}

.action-btn.edit:hover {
  background: #1890ff;
  color: white;
}

.action-btn.toggle {
  background: #f6ffed;
  color: #52c41a;
}

.action-btn.toggle:hover {
  background: #52c41a;
  color: white;
}

.action-btn.delete {
  background: #fff2f0;
  color: #ff4d4f;
}

.action-btn.delete:hover {
  background: #ff4d4f;
  color: white;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-state h3 {
  color: #262626;
  font-size: 20px;
  margin-bottom: 12px;
}

.empty-state p {
  color: #8c8c8c;
  margin-bottom: 24px;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 24px;
  border-top: 1px solid #f0f0f0;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #595959;
  font-size: 14px;
}

/* 成功消息 */
.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #52c41a;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 弹窗 */
.form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  z-index: 1001;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .health-reminders-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .today-reminders {
    grid-template-columns: 1fr;
  }

  .reminder-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .reminder-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .reminder-actions {
    align-self: flex-end;
  }

  .header-controls {
    flex-direction: column;
    width: 100%;
  }

  .filter-select {
    width: 100%;
  }
}
</style>
