<template>
  <div class="change-password-form">
    <div class="form-header">
      <h3>修改密码</h3>
      <button @click="$emit('close')" class="close-btn">✕</button>
    </div>

    <div class="form-content">
      <div class="security-notice">
        <div class="notice-icon">🔐</div>
        <div class="notice-text">
          <h4>安全提示</h4>
          <p>为了您的账户安全，请设置一个强密码。密码长度应为6-20个字符。</p>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="password-form">
        <!-- 当前密码 -->
        <div class="form-group">
          <label for="currentPassword">当前密码 *</label>
          <div class="password-input-wrapper">
            <input
              id="currentPassword"
              v-model="form.currentPassword"
              :type="showCurrentPassword ? 'text' : 'password'"
              placeholder="请输入当前密码"
              required
              minlength="6"
              maxlength="20"
              class="form-input"
            />
            <button
              type="button"
              @click="showCurrentPassword = !showCurrentPassword"
              class="password-toggle"
            >
              {{ showCurrentPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
          <span class="help-text">请输入您当前使用的密码</span>
        </div>

        <!-- 新密码 -->
        <div class="form-group">
          <label for="newPassword">新密码 *</label>
          <div class="password-input-wrapper">
            <input
              id="newPassword"
              v-model="form.newPassword"
              :type="showNewPassword ? 'text' : 'password'"
              placeholder="请输入新密码"
              required
              minlength="6"
              maxlength="20"
              class="form-input"
            />
            <button
              type="button"
              @click="showNewPassword = !showNewPassword"
              class="password-toggle"
            >
              {{ showNewPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
          <div class="password-strength">
            <div class="strength-bar">
              <div 
                class="strength-fill" 
                :class="passwordStrengthClass"
                :style="{ width: passwordStrengthWidth }"
              ></div>
            </div>
            <span class="strength-text">{{ passwordStrengthText }}</span>
          </div>
        </div>

        <!-- 确认新密码 -->
        <div class="form-group">
          <label for="confirmPassword">确认新密码 *</label>
          <div class="password-input-wrapper">
            <input
              id="confirmPassword"
              v-model="form.confirmPassword"
              :type="showConfirmPassword ? 'text' : 'password'"
              placeholder="请再次输入新密码"
              required
              minlength="6"
              maxlength="20"
              class="form-input"
              :class="{ 'error': form.confirmPassword && !passwordsMatch }"
            />
            <button
              type="button"
              @click="showConfirmPassword = !showConfirmPassword"
              class="password-toggle"
            >
              {{ showConfirmPassword ? '👁️' : '👁️‍🗨️' }}
            </button>
          </div>
          <span v-if="form.confirmPassword && !passwordsMatch" class="error-text">
            两次输入的密码不一致
          </span>
          <span v-else class="help-text">请再次输入新密码以确认</span>
        </div>

        <!-- 表单按钮 -->
        <div class="form-actions">
          <button type="button" @click="resetForm" class="btn-reset">
            重置
          </button>
          <button type="submit" :disabled="!isFormValid || loading" class="btn-submit">
            <span v-if="loading">修改中...</span>
            <span v-else>确认修改</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { changePassword } from '@/api/user'

// Props & Emits
const emit = defineEmits(['close', 'success'])

// 响应式数据
const loading = ref(false)
const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)

const form = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 计算属性
const isFormValid = computed(() => {
  return form.value.currentPassword &&
         form.value.newPassword &&
         form.value.confirmPassword &&
         form.value.newPassword.length >= 6 &&
         form.value.newPassword.length <= 20 &&
         passwordsMatch.value &&
         form.value.newPassword !== form.value.currentPassword
})

const passwordsMatch = computed(() => {
  return form.value.newPassword === form.value.confirmPassword
})

// 密码强度检测
const passwordStrength = computed(() => {
  const password = form.value.newPassword
  if (!password) return 0
  
  let score = 0
  
  // 长度检查
  if (password.length >= 8) score += 1
  if (password.length >= 12) score += 1
  
  // 复杂度检查
  if (/[a-z]/.test(password)) score += 1
  if (/[A-Z]/.test(password)) score += 1
  if (/[0-9]/.test(password)) score += 1
  if (/[^A-Za-z0-9]/.test(password)) score += 1
  
  return Math.min(score, 4)
})

const passwordStrengthClass = computed(() => {
  switch (passwordStrength.value) {
    case 0:
    case 1: return 'weak'
    case 2: return 'fair'
    case 3: return 'good'
    case 4: return 'strong'
    default: return 'weak'
  }
})

const passwordStrengthWidth = computed(() => {
  return `${(passwordStrength.value / 4) * 100}%`
})

const passwordStrengthText = computed(() => {
  switch (passwordStrength.value) {
    case 0: return '请输入密码'
    case 1: return '密码强度：弱'
    case 2: return '密码强度：一般'
    case 3: return '密码强度：良好'
    case 4: return '密码强度：强'
    default: return ''
  }
})

// 方法
const resetForm = () => {
  form.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
  showCurrentPassword.value = false
  showNewPassword.value = false
  showConfirmPassword.value = false
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  loading.value = true
  try {
    const response = await changePassword(form.value)
    
    if (response.data.code === 200) {
      emit('success', '密码修改成功！请重新登录。')
      resetForm()
    } else {
      alert('密码修改失败：' + response.data.message)
    }
  } catch (error) {
    console.error('修改密码失败:', error)
    if (error.response?.data?.message) {
      alert('密码修改失败：' + error.response.data.message)
    } else {
      alert('密码修改失败，请重试')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.change-password-form {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  max-width: 500px;
  width: 100%;
}

/* 表单头部 */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.form-header h3 {
  color: #262626;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  color: #8c8c8c;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #ff4d4f;
  color: white;
}

/* 表单内容 */
.form-content {
  padding: 28px;
}

/* 安全提示 */
.security-notice {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #f0f8ff;
  border: 1px solid #91d5ff;
  border-radius: 8px;
  margin-bottom: 24px;
}

.notice-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.notice-text h4 {
  color: #1890ff;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.notice-text p {
  color: #595959;
  font-size: 13px;
  margin: 0;
  line-height: 1.4;
}

/* 表单 */
.password-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  color: #262626;
  font-weight: 600;
  font-size: 14px;
}

.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 12px 50px 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  color: #262626;
  background: white;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.form-input.error {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #8c8c8c;
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: #1890ff;
}

.help-text {
  color: #8c8c8c;
  font-size: 12px;
}

.error-text {
  color: #ff4d4f;
  font-size: 12px;
}

/* 密码强度 */
.password-strength {
  display: flex;
  align-items: center;
  gap: 12px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  background: #ff4d4f;
}

.strength-fill.fair {
  background: #fa8c16;
}

.strength-fill.good {
  background: #1890ff;
}

.strength-fill.strong {
  background: #52c41a;
}

.strength-text {
  font-size: 12px;
  color: #8c8c8c;
  white-space: nowrap;
}

/* 表单按钮 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.btn-reset,
.btn-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-reset {
  background: #f5f5f5;
  color: #595959;
  border: 1px solid #d9d9d9;
}

.btn-reset:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.btn-submit {
  background: #1890ff;
  color: white;
}

.btn-submit:hover:not(:disabled) {
  background: #096dd9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.btn-submit:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .change-password-form {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .form-content {
    padding: 20px;
  }

  .security-notice {
    flex-direction: column;
    text-align: center;
  }

  .password-strength {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .strength-text {
    text-align: center;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-reset,
  .btn-submit {
    width: 100%;
  }
}
</style>

<style scoped>
.change-password-form {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  max-width: 500px;
  width: 100%;
}

/* 表单头部 */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.form-header h3 {
  color: #262626;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  color: #8c8c8c;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #ff4d4f;
  color: white;
}

/* 表单内容 */
.form-content {
  padding: 28px;
}

/* 安全提示 */
.security-notice {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #f0f8ff;
  border: 1px solid #91d5ff;
  border-radius: 8px;
  margin-bottom: 24px;
}

.notice-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.notice-text h4 {
  color: #1890ff;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.notice-text p {
  color: #595959;
  font-size: 13px;
  margin: 0;
  line-height: 1.4;
}

/* 表单 */
.password-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  color: #262626;
  font-weight: 600;
  font-size: 14px;
}

.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 12px 50px 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  color: #262626;
  background: white;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.form-input.error {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #8c8c8c;
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: #1890ff;
}

.help-text {
  color: #8c8c8c;
  font-size: 12px;
}

.error-text {
  color: #ff4d4f;
  font-size: 12px;
}

/* 密码强度 */
.password-strength {
  display: flex;
  align-items: center;
  gap: 12px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  background: #ff4d4f;
}

.strength-fill.fair {
  background: #fa8c16;
}

.strength-fill.good {
  background: #1890ff;
}

.strength-fill.strong {
  background: #52c41a;
}

.strength-text {
  font-size: 12px;
  color: #8c8c8c;
  white-space: nowrap;
}
</style>
