<template>
  <div class="debug-page">
    <div class="debug-container">
      <h1>用户信息调试页面</h1>
      
      <div class="info-section">
        <h2>用户基本信息</h2>
        <div class="info-grid">
          <div class="info-item">
            <label>是否已登录:</label>
            <span :class="{ success: userStore.isLoggedIn, error: !userStore.isLoggedIn }">
              {{ userStore.isLoggedIn ? '是' : '否' }}
            </span>
          </div>
          
          <div class="info-item">
            <label>用户角色:</label>
            <span>{{ userStore.userRole || '未设置' }}</span>
          </div>
          
          <div class="info-item">
            <label>是否为居民:</label>
            <span :class="{ success: userStore.isResident, error: !userStore.isResident }">
              {{ userStore.isResident ? '是' : '否' }}
            </span>
          </div>
          
          <div class="info-item">
            <label>是否为医生:</label>
            <span :class="{ success: userStore.isDoctor, error: !userStore.isDoctor }">
              {{ userStore.isDoctor ? '是' : '否' }}
            </span>
          </div>
          
          <div class="info-item">
            <label>是否为管理员:</label>
            <span :class="{ success: userStore.isAdmin, error: !userStore.isAdmin }">
              {{ userStore.isAdmin ? '是' : '否' }}
            </span>
          </div>
        </div>
      </div>

      <div class="info-section">
        <h2>用户详细信息</h2>
        <pre class="json-display">{{ JSON.stringify(userStore.userInfo, null, 2) }}</pre>
      </div>

      <div class="info-section">
        <h2>Token信息</h2>
        <div class="info-item">
          <label>Token:</label>
          <span class="token-display">{{ userStore.token ? userStore.token.substring(0, 50) + '...' : '无' }}</span>
        </div>
      </div>

      <div class="actions-section">
        <h2>功能测试</h2>
        <div class="action-buttons">
          <button @click="testConsultationNavigation" class="test-btn">
            测试问诊页面跳转
          </button>
          
          <button @click="testDoctorNavigation" class="test-btn">
            测试医生页面跳转
          </button>
          
          <button @click="goToLogin" class="test-btn secondary">
            前往登录页面
          </button>
          
          <button @click="goToHome" class="test-btn secondary">
            返回首页
          </button>
        </div>
      </div>

      <div class="logs-section">
        <h2>操作日志</h2>
        <div class="logs-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'

const userStore = useUserStore()
const router = useRouter()
const logs = ref([])

const addLog = (message) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message
  })
  console.log(message)
}

const testConsultationNavigation = () => {
  addLog('开始测试问诊页面跳转...')
  addLog(`用户角色: ${userStore.userRole}`)
  addLog(`是否为居民: ${userStore.isResident}`)
  addLog(`是否已登录: ${userStore.isLoggedIn}`)
  
  if (!userStore.isLoggedIn) {
    addLog('❌ 用户未登录，无法跳转')
    return
  }
  
  if (!userStore.isResident) {
    addLog('❌ 用户不是居民角色，无法访问问诊页面')
    return
  }
  
  addLog('✅ 权限检查通过，尝试跳转到问诊页面...')
  router.push('/consultations').then(() => {
    addLog('✅ 跳转成功')
  }).catch(error => {
    addLog(`❌ 跳转失败: ${error.message}`)
  })
}

const testDoctorNavigation = () => {
  addLog('开始测试医生页面跳转...')
  addLog(`用户角色: ${userStore.userRole}`)
  addLog(`是否为医生: ${userStore.isDoctor}`)
  addLog(`是否已登录: ${userStore.isLoggedIn}`)
  
  if (!userStore.isLoggedIn) {
    addLog('❌ 用户未登录，无法跳转')
    return
  }
  
  if (!userStore.isDoctor) {
    addLog('❌ 用户不是医生角色，无法访问医生页面')
    return
  }
  
  addLog('✅ 权限检查通过，尝试跳转到医生问诊页面...')
  router.push('/doctor/consultations').then(() => {
    addLog('✅ 跳转成功')
  }).catch(error => {
    addLog(`❌ 跳转失败: ${error.message}`)
  })
}

const goToLogin = () => {
  addLog('跳转到登录页面...')
  router.push('/login')
}

const goToHome = () => {
  addLog('返回首页...')
  router.push('/')
}

// 页面加载时记录初始状态
addLog('页面加载完成')
addLog(`当前用户角色: ${userStore.userRole}`)
addLog(`登录状态: ${userStore.isLoggedIn}`)
</script>

<style scoped>
.debug-page {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

.debug-container {
  max-width: 1000px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  color: #1e293b;
  margin-bottom: 32px;
}

.info-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-section h2 {
  color: #374151;
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.info-item label {
  font-weight: 600;
  color: #374151;
}

.success {
  color: #16a34a;
  font-weight: 600;
}

.error {
  color: #dc2626;
  font-weight: 600;
}

.json-display {
  background: #1e293b;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.5;
}

.token-display {
  font-family: monospace;
  font-size: 12px;
  color: #64748b;
  word-break: break-all;
}

.actions-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.test-btn {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-btn:hover {
  background: #2563eb;
}

.test-btn.secondary {
  background: #6b7280;
}

.test-btn.secondary:hover {
  background: #4b5563;
}

.logs-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
}

.log-item {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 14px;
}

.log-time {
  color: #64748b;
  font-family: monospace;
  flex-shrink: 0;
}

.log-message {
  color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .debug-page {
    padding: 16px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    grid-template-columns: 1fr;
  }
}
</style>
