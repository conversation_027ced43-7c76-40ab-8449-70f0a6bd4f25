import api from './index'

/**
 * 创建问诊会话（居民）
 * API测试报告确认的接口格式
 * @param {Object} data - 问诊数据
 * @param {number} data.doctorId - 医生ID
 * @returns {Promise} API响应
 */
export const createConsultation = (data) => {
  console.log('=== 创建问诊会话API调用 ===')
  console.log('请求数据:', data)
  return api.post('/consultations', data)
}

// 获取问诊列表
export const getConsultations = (params) => {
  return api.get('/consultations', { params })
}

// 发送消息
export const sendMessage = (consultationId, data) => {
  return api.post(`/consultations/${consultationId}/messages`, data)
}

// 获取消息历史记录
export const getMessages = (consultationId, params) => {
  return api.get(`/consultations/${consultationId}/messages`, { params })
}

// 完成问诊（医生）
export const completeConsultation = (consultationId) => {
  return api.put(`/consultations/${consultationId}/complete`)
}

// 获取医生列表（用于发起问诊）
export const getDoctorList = (params) => {
  // 使用预约系统的医生搜索接口，但需要处理参数格式
  const searchParams = {
    page: params.page || 1,
    size: params.size || 100
  }

  // 如果有姓名搜索，使用name参数
  if (params.name) {
    searchParams.name = params.name
  }

  // 如果有科室筛选，使用department参数
  if (params.department) {
    searchParams.department = params.department
  }

  // 如果有职称筛选，使用title参数
  if (params.title) {
    searchParams.title = params.title
  }

  return api.get('/appointments/doctors/search', { params: searchParams })
}
