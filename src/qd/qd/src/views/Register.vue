<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1>社区健康管理系统</h1>
        <p>用户注册</p>
      </div>

      <form @submit.prevent="handleRegister" class="register-form">
        <div class="form-group">
          <label for="phone">手机号</label>
          <input
            id="phone"
            v-model="registerForm.phone_number"
            type="tel"
            placeholder="请输入手机号"
            :class="{ 'error': errors.phone_number }"
            @blur="validatePhone"
          />
          <span v-if="errors.phone_number" class="error-message">
            {{ errors.phone_number }}
          </span>
        </div>

        <div class="form-group">
          <label for="nickname">昵称</label>
          <input
            id="nickname"
            v-model="registerForm.nickname"
            type="text"
            placeholder="请输入昵称"
            :class="{ 'error': errors.nickname }"
            @blur="validateNickname"
          />
          <span v-if="errors.nickname" class="error-message">
            {{ errors.nickname }}
          </span>
        </div>

        <div class="form-group">
          <label for="role">用户类型</label>
          <select
            id="role"
            v-model="registerForm.role"
            :class="{ 'error': errors.role }"
            @change="validateRole"
          >
            <option value="">请选择用户类型</option>
            <option value="RESIDENT">居民</option>
            <option value="DOCTOR">医生</option>
          </select>
          <span v-if="errors.role" class="error-message">
            {{ errors.role }}
          </span>
        </div>

        <div class="form-group">
          <label for="password">密码</label>
          <input
            id="password"
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码（至少6位）"
            :class="{ 'error': errors.password }"
            @blur="validatePassword"
          />
          <span v-if="errors.password" class="error-message">
            {{ errors.password }}
          </span>
        </div>

        <div class="form-group">
          <label for="confirmPassword">确认密码</label>
          <input
            id="confirmPassword"
            v-model="confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            :class="{ 'error': errors.confirmPassword }"
            @blur="validateConfirmPassword"
          />
          <span v-if="errors.confirmPassword" class="error-message">
            {{ errors.confirmPassword }}
          </span>
        </div>

        <div class="button-group">
          <button
            type="submit"
            class="register-btn"
            :disabled="isLoading || !isFormValid"
          >
            {{ isLoading ? '注册中...' : '注册' }}
          </button>

          <router-link to="/login" class="login-btn">
            登录
          </router-link>
        </div>
      </form>

      <!-- 错误提示 -->
      <div v-if="registerError" class="error-alert">
        {{ registerError }}
      </div>

      <!-- 成功提示 -->
      <div v-if="registerSuccess" class="success-alert">
        注册成功！请登录使用系统
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const registerForm = ref({
  phone_number: '',
  nickname: '',
  password: '',
  role: ''
})

const confirmPassword = ref('')

// 表单验证错误
const errors = ref({
  phone_number: '',
  nickname: '',
  password: '',
  confirmPassword: '',
  role: ''
})

// 注册状态
const registerError = ref('')
const registerSuccess = ref(false)

// 加载状态
const isLoading = computed(() => userStore.isLoading)

// 表单验证函数
const validatePhone = () => {
  const phone = registerForm.value.phone_number.trim()
  if (!phone) {
    errors.value.phone_number = '请输入手机号'
    return false
  }
  if (!/^1[3-9]\d{9}$/.test(phone)) {
    errors.value.phone_number = '请输入正确的手机号格式'
    return false
  }
  errors.value.phone_number = ''
  return true
}

const validateNickname = () => {
  const nickname = registerForm.value.nickname.trim()
  if (!nickname) {
    errors.value.nickname = '请输入昵称'
    return false
  }
  if (nickname.length < 2 || nickname.length > 20) {
    errors.value.nickname = '昵称长度应在2-20个字符之间'
    return false
  }
  errors.value.nickname = ''
  return true
}

const validateRole = () => {
  if (!registerForm.value.role) {
    errors.value.role = '请选择用户类型'
    return false
  }
  errors.value.role = ''
  return true
}

const validatePassword = () => {
  const password = registerForm.value.password
  if (!password) {
    errors.value.password = '请输入密码'
    return false
  }
  if (password.length < 6) {
    errors.value.password = '密码长度不能少于6位'
    return false
  }
  errors.value.password = ''
  return true
}

const validateConfirmPassword = () => {
  if (!confirmPassword.value) {
    errors.value.confirmPassword = '请确认密码'
    return false
  }
  if (confirmPassword.value !== registerForm.value.password) {
    errors.value.confirmPassword = '两次输入的密码不一致'
    return false
  }
  errors.value.confirmPassword = ''
  return true
}

// 表单是否有效
const isFormValid = computed(() => {
  return registerForm.value.phone_number && 
         registerForm.value.nickname && 
         registerForm.value.password && 
         registerForm.value.role &&
         confirmPassword.value &&
         !errors.value.phone_number && 
         !errors.value.nickname &&
         !errors.value.password && 
         !errors.value.confirmPassword &&
         !errors.value.role
})

// 处理注册
const handleRegister = async () => {
  // 清除之前的状态
  registerError.value = ''
  registerSuccess.value = false
  
  // 验证所有字段
  const validations = [
    validatePhone(),
    validateNickname(),
    validateRole(),
    validatePassword(),
    validateConfirmPassword()
  ]
  
  if (!validations.every(v => v)) {
    return
  }
  
  try {
    const result = await userStore.register(registerForm.value)
    
    if (result.success) {
      registerSuccess.value = true
      // 3秒后跳转到登录页
      setTimeout(() => {
        router.push('/login')
      }, 2000)
    } else {
      registerError.value = result.message
    }
  } catch (error) {
    registerError.value = '注册失败，请稍后重试'
    console.error('注册错误:', error)
  }
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  margin: 0;
  position: fixed;
  top: 0;
  left: 0;
}

.register-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  padding: 50px 60px;
  width: 90%;
  max-width: 500px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin: 20px;
}

.register-header {
  text-align: center;
  margin-bottom: 35px;
}

.register-header h1 {
  color: #1565c0;
  font-size: 30px;
  margin-bottom: 12px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.register-header p {
  color: #546e7a;
  font-size: 18px;
  margin: 0;
  font-weight: 400;
}

.register-form {
  width: 100%;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 10px;
  color: #1565c0;
  font-weight: 600;
  font-size: 15px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e3f2fd;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.8);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #42a5f5;
  background: white;
  box-shadow: 0 0 0 3px rgba(66, 165, 245, 0.1);
}

.form-group input.error,
.form-group select.error {
  border-color: #f44336;
  background: rgba(244, 67, 54, 0.05);
}

.error-message {
  color: #f44336;
  font-size: 14px;
  margin-top: 8px;
  display: block;
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.register-btn {
  flex: 1;
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

.register-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
}

.register-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.login-btn {
  flex: 1;
  padding: 16px 24px;
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

.login-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.error-alert {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  color: #c62828;
  padding: 16px;
  border-radius: 12px;
  margin-top: 25px;
  text-align: center;
  border: 1px solid #ffcdd2;
  font-weight: 500;
}

.success-alert {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  color: #2e7d32;
  padding: 16px;
  border-radius: 12px;
  margin-top: 25px;
  text-align: center;
  border: 1px solid #c8e6c9;
  font-weight: 500;
}


</style>
