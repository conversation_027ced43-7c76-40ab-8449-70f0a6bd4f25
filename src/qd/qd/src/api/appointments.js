import api from './index'

/**
 * 获取医生的预约患者列表
 * @param {Object} params - 查询参数
 * @param {string} params.status - 预约状态 (BOOKED/COMPLETED/CANCELLED)
 * @param {number} params.page - 页码 (从0开始)
 * @param {number} params.size - 每页大小
 * @param {string} params.date - 指定日期 (YYYY-MM-DD)
 * @returns {Promise} 预约列表响应
 */
export const getDoctorAppointments = (params = {}) => {
  return api.get('/doctor/appointments/my', { params })
}

/**
 * 确认患者预约
 * @param {number} appointmentId - 预约ID
 * @returns {Promise} 确认预约响应
 */
export const confirmAppointment = (appointmentId) => {
  return api.post(`/doctor/appointments/${appointmentId}/confirm`)
}

/**
 * 完成诊疗
 * API测试报告确认的接口格式
 * @param {number} appointmentId - 预约ID
 * @param {Object} data - 完成诊疗数据
 * @param {string} data.notes - 诊疗备注
 * @returns {Promise} 完成诊疗响应
 */
export const completeAppointment = (appointmentId, data = {}) => {
  console.log('=== 完成诊疗API调用 ===')
  console.log('预约ID:', appointmentId)
  console.log('诊疗数据:', data)
  return api.post(`/doctor/appointments/${appointmentId}/complete`, data)
}

/**
 * 添加诊疗记录
 * @param {number} appointmentId - 预约ID
 * @param {Object} recordData - 诊疗记录数据
 * @param {string} recordData.diagnosis - 诊断结果
 * @param {string} recordData.treatment - 治疗方案
 * @param {string} recordData.prescription - 处方信息
 * @returns {Promise} 添加诊疗记录响应
 */
export const addMedicalRecord = (appointmentId, recordData) => {
  return api.post(`/doctor/appointments/${appointmentId}/record`, recordData)
}

/**
 * 获取患者病历记录
 * @param {number} patientId - 患者ID
 * @returns {Promise} 患者病历响应
 */
export const getPatientRecords = (patientId) => {
  return api.get(`/doctor/patients/${patientId}/records`)
}

/**
 * 获取预约详情
 * @param {number} appointmentId - 预约ID
 * @returns {Promise} 预约详情响应
 */
export const getAppointmentDetail = (appointmentId) => {
  return api.get(`/doctor/appointments/${appointmentId}`)
}

/**
 * 取消预约
 * @param {number} appointmentId - 预约ID
 * @param {Object} data - 取消原因
 * @param {string} data.reason - 取消原因
 * @returns {Promise} 取消预约响应
 */
export const cancelAppointment = (appointmentId, data = {}) => {
  return api.post(`/doctor/appointments/${appointmentId}/cancel`, data)
}

/**
 * 获取今日预约统计
 * @param {string} date - 日期 (YYYY-MM-DD)，默认今天
 * @returns {Promise} 预约统计响应
 */
export const getTodayAppointmentStats = (date = null) => {
  const params = {}
  if (date) params.date = date
  
  return api.get('/doctor/appointments/stats/today', { params })
}

/**
 * 获取预约统计信息
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise} 统计信息响应
 */
export const getAppointmentStats = (params = {}) => {
  return api.get('/doctor/appointments/stats', { params })
}

/**
 * 搜索预约
 * @param {Object} params - 搜索参数
 * @param {string} params.keyword - 关键词（患者姓名、手机号等）
 * @param {string} params.status - 状态筛选
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @returns {Promise} 搜索结果响应
 */
export const searchAppointments = (params = {}) => {
  return api.get('/doctor/appointments/search', { params })
}

/**
 * 批量操作预约
 * @param {Object} data - 批量操作数据
 * @param {Array} data.appointmentIds - 预约ID数组
 * @param {string} data.action - 操作类型 (confirm/complete/cancel)
 * @param {Object} data.params - 操作参数
 * @returns {Promise} 批量操作响应
 */
export const batchOperateAppointments = (data) => {
  return api.post('/doctor/appointments/batch', data)
}

/**
 * 获取患者基本信息
 * @param {number} patientId - 患者ID
 * @returns {Promise} 患者信息响应
 */
export const getPatientInfo = (patientId) => {
  return api.get(`/doctor/patients/${patientId}/info`)
}

/**
 * 获取预约时间段
 * @param {string} date - 日期 (YYYY-MM-DD)
 * @returns {Promise} 时间段响应
 */
export const getAppointmentTimeSlots = (date) => {
  return api.get('/doctor/appointments/time-slots', {
    params: { date }
  })
}

/**
 * 更新预约备注
 * @param {number} appointmentId - 预约ID
 * @param {Object} data - 备注数据
 * @param {string} data.notes - 备注内容
 * @returns {Promise} 更新备注响应
 */
export const updateAppointmentNotes = (appointmentId, data) => {
  return api.put(`/doctor/appointments/${appointmentId}/notes`, data)
}

/**
 * 获取诊疗记录详情
 * @param {number} recordId - 记录ID
 * @returns {Promise} 诊疗记录详情响应
 */
export const getMedicalRecordDetail = (recordId) => {
  return api.get(`/doctor/medical-records/${recordId}`)
}

/**
 * 更新诊疗记录
 * @param {number} recordId - 记录ID
 * @param {Object} recordData - 诊疗记录数据
 * @param {string} recordData.diagnosis - 诊断结果
 * @param {string} recordData.treatment - 治疗方案
 * @param {string} recordData.prescription - 处方信息
 * @returns {Promise} 更新诊疗记录响应
 */
export const updateMedicalRecord = (recordId, recordData) => {
  return api.put(`/doctor/medical-records/${recordId}`, recordData)
}

/**
 * 删除诊疗记录
 * @param {number} recordId - 记录ID
 * @returns {Promise} 删除诊疗记录响应
 */
export const deleteMedicalRecord = (recordId) => {
  return api.delete(`/doctor/medical-records/${recordId}`)
}

/**
 * 导出预约数据
 * @param {Object} params - 导出参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {string} params.status - 状态筛选
 * @param {string} params.format - 导出格式 (excel/pdf)
 * @returns {Promise} 导出响应
 */
export const exportAppointments = (params = {}) => {
  return api.get('/doctor/appointments/export', {
    params,
    responseType: 'blob'
  })
}

/**
 * 获取预约提醒设置
 * @returns {Promise} 提醒设置响应
 */
export const getAppointmentReminders = () => {
  return api.get('/doctor/appointments/reminders')
}

/**
 * 更新预约提醒设置
 * @param {Object} settings - 提醒设置
 * @param {boolean} settings.emailEnabled - 邮件提醒
 * @param {boolean} settings.smsEnabled - 短信提醒
 * @param {number} settings.advanceMinutes - 提前提醒分钟数
 * @returns {Promise} 更新提醒设置响应
 */
export const updateAppointmentReminders = (settings) => {
  return api.put('/doctor/appointments/reminders', settings)
}

// ==================== 患者端预约接口 ====================

/**
 * 获取所有科室列表
 * @returns {Promise} 科室列表响应
 */
export const getDepartments = () => {
  return api.get('/appointments/departments')
}

/**
 * 搜索医生
 * @param {Object} params - 搜索参数
 * @param {string} params.name - 医生姓名
 * @param {number} params.departmentId - 科室ID
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @returns {Promise} 医生搜索响应
 */
export const searchDoctors = (params = {}) => {
  return api.get('/appointments/doctors/search', { params })
}

/**
 * 获取科室下的医生列表
 * @param {number} departmentId - 科室ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @returns {Promise} 医生列表响应
 */
export const getDepartmentDoctors = (departmentId, params = {}) => {
  return api.get(`/appointments/departments/${departmentId}/doctors`, { params })
}

/**
 * 获取医生详情
 * @param {number} doctorId - 医生ID
 * @returns {Promise} 医生详情响应
 */
export const getDoctorDetail = (doctorId) => {
  return api.get(`/appointments/doctors/${doctorId}`)
}

/**
 * 获取医生排班信息
 * @param {number} doctorId - 医生用户ID
 * @param {Object} params - 查询参数
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @returns {Promise} 排班信息响应
 */
export const getDoctorSchedules = (doctorId, params = {}) => {
  return api.get(`/appointments/doctors/${doctorId}/schedules`, { params })
}

/**
 * 获取可预约排班
 * @param {Object} params - 查询参数
 * @param {string} params.date - 日期
 * @param {number} params.departmentId - 科室ID
 * @param {number} params.doctorId - 医生ID
 * @returns {Promise} 可预约排班响应
 */
export const getAvailableSchedules = (params = {}) => {
  return api.get('/appointments/schedules/available', { params })
}

/**
 * 创建预约
 * @param {Object} appointmentData - 预约数据
 * @param {number} appointmentData.scheduleId - 排班ID
 * @param {number} appointmentData.profileId - 健康档案ID
 * @param {string} appointmentData.notes - 预约备注
 * @returns {Promise} 创建预约响应
 */
export const createAppointment = (appointmentData) => {
  return api.post('/appointments', appointmentData)
}

/**
 * 取消患者预约
 * @param {number} appointmentId - 预约ID
 * @returns {Promise} 取消预约响应
 */
export const cancelPatientAppointment = (appointmentId) => {
  return api.post(`/appointments/${appointmentId}/cancel`)
}

/**
 * 获取我的预约列表（患者）
 * API测试报告确认的接口格式
 * @param {Object} params - 查询参数
 * @param {string} params.status - 预约状态 (BOOKED/COMPLETED/CANCELLED)
 * @param {number} params.page - 页码（从1开始）
 * @param {number} params.size - 每页大小
 * @returns {Promise} 我的预约列表响应
 */
export const getMyAppointments = (params = {}) => {
  console.log('=== 获取我的预约记录API调用 ===')
  console.log('查询参数:', params)

  // 确保页码从1开始（与API测试报告一致）
  const queryParams = {
    page: params.page || 1,
    size: params.size || 10,
    ...params
  }

  return api.get('/appointments/my', { params: queryParams })
}

/**
 * 获取预约详情
 * @param {number} appointmentId - 预约ID
 * @returns {Promise} 预约详情响应
 */
export const getPatientAppointmentDetail = (appointmentId) => {
  return api.get(`/appointments/${appointmentId}`)
}

/**
 * 获取即将到来的预约
 * @param {Object} params - 查询参数
 * @param {number} params.days - 未来天数，默认7天
 * @returns {Promise} 即将到来的预约响应
 */
export const getUpcomingAppointments = (params = {}) => {
  return api.get('/appointments/upcoming', { params })
}
