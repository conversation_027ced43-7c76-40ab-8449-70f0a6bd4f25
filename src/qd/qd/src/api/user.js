import api from './index'

/**
 * 用户登录
 * @param {Object} loginData - 登录数据
 * @param {string} loginData.phone_number - 手机号
 * @param {string} loginData.password - 密码
 * @returns {Promise} 登录响应
 */
export const login = (loginData) => {
  return api.post('/user/login', {
    phoneNumber: loginData.phone_number,
    password: loginData.password
  })
}

/**
 * 用户注册
 * @param {Object} registerData - 注册数据
 * @param {string} registerData.phone_number - 手机号
 * @param {string} registerData.password - 密码
 * @param {string} registerData.nickname - 昵称
 * @param {string} registerData.role - 角色 (RESIDENT, DOCTOR, ADMIN)
 * @returns {Promise} 注册响应
 */
export const register = (registerData) => {
  return api.post('/user/register', {
    phoneNumber: registerData.phone_number,
    password: registerData.password,
    nickname: registerData.nickname,
    role: registerData.role
  })
}

/**
 * 获取用户信息
 * @returns {Promise} 用户信息响应
 */
export const getUserInfo = () => {
  return api.get('/user/profile')
}

/**
 * 更新用户个人信息
 * @param {Object} profileData - 个人信息数据
 * @param {string} profileData.nickname - 昵称
 * @param {string} profileData.phoneNumber - 手机号
 * @param {string} profileData.avatarUrl - 头像URL
 * @param {string} profileData.realName - 真实姓名 (仅医生)
 * @returns {Promise} 更新响应
 */
export const updateUserProfile = (profileData) => {
  return api.put('/user/profile', profileData)
}

/**
 * 修改密码
 * @param {Object} passwordData - 密码数据
 * @param {string} passwordData.currentPassword - 当前密码
 * @param {string} passwordData.newPassword - 新密码
 * @param {string} passwordData.confirmPassword - 确认新密码
 * @returns {Promise} 修改密码响应
 */
export const changePassword = (passwordData) => {
  return api.post('/user/change-password', passwordData)
}

/**
 * 医生专用密码修改
 * @param {Object} passwordData - 密码数据
 * @param {string} passwordData.currentPassword - 当前密码
 * @param {string} passwordData.newPassword - 新密码
 * @param {string} passwordData.confirmPassword - 确认新密码
 * @returns {Promise} 修改密码响应
 */
export const changeDoctorPassword = (passwordData) => {
  return api.post('/user/doctor/change-password', passwordData)
}

/**
 * 刷新token
 * @param {string} refreshToken - 刷新token
 * @returns {Promise} 刷新token响应
 */
export const refreshToken = (refreshToken) => {
  return api.post('/auth/refresh', { refreshToken })
}

/**
 * 用户登出
 * @returns {Promise} 登出响应
 */
export const logout = () => {
  return api.post('/user/logout')
}
