<template>
  <div class="dashboard-layout">
    <!-- 左侧导航栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <div class="logo-icon">🏥</div>
          <h2>健康管家</h2>
        </div>
      </div>

      <nav class="sidebar-nav">
        <div class="nav-item active" @click="setActiveTab('dashboard')">
          <div class="nav-icon">🏠</div>
          <span>我的主页</span>
        </div>
        <div class="nav-item" @click="navigateToProfile">
          <div class="nav-icon">👤</div>
          <span>健康档案</span>
        </div>
        <div class="nav-item" @click="navigateToAppointments">
          <div class="nav-icon">📅</div>
          <span>预约挂号</span>
        </div>

        <div class="nav-item" @click="navigateToRecords">
          <div class="nav-icon">📊</div>
          <span>健康数据</span>
        </div>
        <div class="nav-item" @click="navigateToReminders">
          <div class="nav-icon">⏰</div>
          <span>健康提醒</span>
        </div>
        <div class="nav-item" @click="setActiveTab('consultations')">
          <div class="nav-icon">💬</div>
          <span>在线问诊</span>
          <div class="message-badge" v-if="pendingConsultations > 0">{{ pendingConsultations }}</div>
        </div>
        <div class="nav-item" @click="setActiveTab('prescriptions')">
          <div class="nav-icon">📋</div>
          <span>查看处方</span>
        </div>
        <div class="nav-item" @click="setActiveTab('records')">
          <div class="nav-icon">📄</div>
          <span>就诊记录</span>
        </div>
        <div class="nav-item" @click="setActiveTab('prescriptions')">
          <div class="nav-icon">💊</div>
          <span>查看处方</span>
        </div>
        <div class="nav-item" @click="setActiveTab('community')">
          <div class="nav-icon">🌟</div>
          <span>社区活动</span>
        </div>
        <div class="nav-item" @click="setActiveTab('settings')">
          <div class="nav-icon">⚙️</div>
          <span>个人设置</span>
        </div>
      </nav>

      <div class="sidebar-footer">
        <div class="user-info">
          <div class="user-avatar">{{ userInfo?.nickname?.charAt(0) || 'U' }}</div>
          <div class="user-details">
            <div class="user-name">{{ userInfo?.nickname || '用户' }}</div>
            <div class="user-role">{{ roleText }}</div>
          </div>
        </div>
        <button @click="handleLogout" class="logout-btn">
          <div class="logout-icon">🚪</div>
          退出
        </button>
      </div>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 我的主页 -->
      <div v-if="activeTab === 'dashboard'" class="dashboard-content">
        <!-- 欢迎语 -->
        <div class="welcome-header">
          <h1>{{ getGreeting() }}, {{ userInfo?.nickname || '用户' }}!</h1>
          <p class="date-info">{{ getCurrentDate() }}</p>
        </div>

        <!-- 卡片网格 -->
        <div class="cards-grid">
          <!-- 核心提醒卡片 -->
          <div class="card highlight-card clickable-card" @click="navigateToAppointments">
            <div class="card-header">
              <h3>📋 下次预约提醒</h3>
            </div>
            <div class="card-content">
              <div class="appointment-info">
                <div class="appointment-main">
                  <div class="doctor-name">李医生 - 心内科</div>
                  <div class="appointment-time">明天 14:30</div>
                </div>
                <div class="appointment-location">第一人民医院 3楼 302诊室</div>
              </div>
              <button class="card-action-btn" @click.stop="navigateToAppointments">查看详情</button>
            </div>
          </div>

          <!-- 近期健康数据卡片 -->
          <div class="card metrics-card">
            <div class="card-header">
              <h3>📈 健康指标</h3>
              <span class="last-update">最后更新: 今天</span>
            </div>
            <div class="card-content">
              <div class="metrics-grid">
                <div class="metric-item">
                  <div class="metric-value">120/80</div>
                  <div class="metric-label">血压 (mmHg)</div>
                  <div class="metric-trend up">↗ 正常</div>
                </div>
                <div class="metric-item">
                  <div class="metric-value">5.8</div>
                  <div class="metric-label">血糖 (mmol/L)</div>
                  <div class="metric-trend stable">→ 稳定</div>
                </div>
              </div>
              <div class="mini-chart">
                <div class="chart-placeholder">📊 7日血压趋势图</div>
              </div>
            </div>
          </div>

          <!-- 快速操作卡片 -->
          <div class="card actions-card">
            <div class="card-header">
              <h3>⚡ 快速操作</h3>
            </div>
            <div class="card-content">
              <div class="quick-actions">
                <button class="quick-action-btn primary" @click="navigateToRecords">
                  <div class="action-icon">📝</div>
                  <span>记录健康数据</span>
                </button>
                <button class="quick-action-btn secondary" @click="navigateToConsultations">
                  <div class="action-icon">💬</div>
                  <span>在线问诊</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 最新动态卡片 -->
          <div class="card updates-card">
            <div class="card-header">
              <h3>📢 最新动态</h3>
            </div>
            <div class="card-content">
              <div class="updates-list">
                <div class="update-item">
                  <div class="update-icon">🎯</div>
                  <div class="update-content">
                    <div class="update-title">春季健康体检活动开始</div>
                    <div class="update-time">2小时前</div>
                  </div>
                </div>
                <div class="update-item">
                  <div class="update-icon">💊</div>
                  <div class="update-content">
                    <div class="update-title">慢性病管理讲座报名</div>
                    <div class="update-time">1天前</div>
                  </div>
                </div>
                <div class="update-item">
                  <div class="update-icon">🏃‍♂️</div>
                  <div class="update-content">
                    <div class="update-title">社区健步走活动</div>
                    <div class="update-time">3天前</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 个人设置页面 -->
      <div v-else-if="activeTab === 'settings'" class="settings-content">
        <div class="settings-header">
          <h1>个人设置</h1>
          <p class="page-description">管理您的个人信息和账户设置</p>
        </div>

        <div class="settings-grid">
          <!-- 个人信息卡片 -->
          <div class="settings-card">
            <div class="card-header">
              <h3>👤 个人信息</h3>
              <button @click="showProfileForm = true" class="edit-btn">
                编辑信息
              </button>
            </div>
            <div class="card-content">
              <div class="profile-preview">
                <div class="profile-avatar">
                  <div
                    class="avatar-img"
                    :style="{ backgroundImage: userInfo?.avatarUrl ? `url(${userInfo.avatarUrl})` : 'none' }"
                  >
                    <span v-if="!userInfo?.avatarUrl" class="avatar-text">
                      {{ userInfo?.nickname?.charAt(0) || 'U' }}
                    </span>
                  </div>
                </div>
                <div class="profile-info">
                  <div class="info-item">
                    <span class="info-label">昵称:</span>
                    <span class="info-value">{{ userInfo?.nickname || '未设置' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">手机号:</span>
                    <span class="info-value">{{ userInfo?.phoneNumber || '未设置' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">用户类型:</span>
                    <span class="info-value">{{ roleText }}</span>
                  </div>
                  <div v-if="userInfo?.isDoctor && userInfo?.realName" class="info-item">
                    <span class="info-label">真实姓名:</span>
                    <span class="info-value">{{ userInfo.realName }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 账户安全卡片 -->
          <div class="settings-card">
            <div class="card-header">
              <h3>🔒 账户安全</h3>
            </div>
            <div class="card-content">
              <div class="security-items">
                <div class="security-item">
                  <div class="security-info">
                    <div class="security-title">登录密码</div>
                    <div class="security-desc">定期更换密码，保护账户安全</div>
                  </div>
                  <button @click="showPasswordForm = true" class="security-btn active">
                    修改密码
                  </button>
                </div>
                <div class="security-item">
                  <div class="security-info">
                    <div class="security-title">手机号绑定</div>
                    <div class="security-desc">{{ userInfo?.phoneNumber || '未绑定' }}</div>
                  </div>
                  <button @click="showProfileForm = true" class="security-btn active">
                    修改手机号
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 系统设置卡片 -->
          <div class="settings-card">
            <div class="card-header">
              <h3>🔧 系统设置</h3>
            </div>
            <div class="card-content">
              <div class="system-items">
                <div class="system-item">
                  <div class="system-info">
                    <div class="system-title">消息通知</div>
                    <div class="system-desc">接收系统消息和健康提醒</div>
                  </div>
                  <label class="switch">
                    <input type="checkbox" checked disabled>
                    <span class="slider"></span>
                  </label>
                </div>
                <div class="system-item">
                  <div class="system-info">
                    <div class="system-title">数据同步</div>
                    <div class="system-desc">自动同步健康数据到云端</div>
                  </div>
                  <label class="switch">
                    <input type="checkbox" checked disabled>
                    <span class="slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 在线问诊页面 -->
      <div v-else-if="activeTab === 'consultations'" class="consultations-content">
        <div class="page-header">
          <h1>在线问诊</h1>
          <p class="page-description">与专业医生进行在线图文咨询</p>

          <!-- 调试信息 -->
          <div class="debug-info" style="background: #f0f8ff; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #007bff;">
            <h4 style="margin: 0 0 10px 0; color: #007bff;">🔍 用户状态调试信息</h4>
            <div style="font-family: monospace; font-size: 12px; line-height: 1.4;">
              <div><strong>登录状态:</strong> {{ userStore.isLoggedIn ? '✅ 已登录' : '❌ 未登录' }}</div>
              <div><strong>用户角色:</strong> {{ userStore.userRole || '未设置' }}</div>
              <div><strong>是否为居民:</strong> {{ userStore.isResident ? '✅ 是' : '❌ 否' }}</div>
              <div><strong>用户昵称:</strong> {{ userStore.userInfo?.nickname || '未设置' }}</div>
              <div><strong>用户ID:</strong> {{ userStore.userInfo?.id || '未设置' }}</div>
            </div>
          </div>

          <button @click="navigateToConsultations" class="primary-btn">
            <span class="btn-icon">💬</span>
            进入问诊页面
          </button>
        </div>

        <!-- 功能介绍 -->
        <div class="feature-intro">
          <div class="intro-card">
            <div class="intro-icon">💬</div>
            <h3>在线问诊功能</h3>
            <p>您可以通过在线问诊功能与专业医生进行实时交流，获得专业的医疗建议。</p>
            <div class="feature-list">
              <div class="feature-item">
                <span class="feature-icon">✅</span>
                <span>选择专业医生</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">✅</span>
                <span>实时图文交流</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">✅</span>
                <span>查看问诊记录</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">✅</span>
                <span>获得专业建议</span>
              </div>
            </div>
            <button @click="navigateToConsultations" class="intro-action-btn">
              立即体验
            </button>
          </div>
        </div>
      </div>

      <!-- 就诊记录页面 -->
      <div v-else-if="activeTab === 'records'" class="page-content">
        <div class="coming-soon">
          <div class="coming-soon-icon">📋</div>
          <h2>就诊记录</h2>
          <p>此功能正在开发中，敬请期待...</p>
          <div class="feature-preview">
            <h4>即将推出的功能：</h4>
            <ul>
              <li>查看历史就诊记录</li>
              <li>诊断结果和治疗方案</li>
              <li>按时间筛选记录</li>
              <li>医院和医生信息</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 查看处方页面 -->
      <div v-else-if="activeTab === 'prescriptions'" class="prescriptions-content">
        <div class="page-header">
          <h1>查看处方</h1>
          <p class="page-description">查看医生为您开具的电子处方</p>

          <button @click="navigateToPrescriptions" class="primary-btn">
            <span class="btn-icon">📋</span>
            进入处方页面
          </button>
        </div>

        <!-- 功能介绍 -->
        <div class="feature-intro">
          <div class="intro-card">
            <div class="intro-icon">📋</div>
            <h3>电子处方功能</h3>
            <p>您可以查看医生为您开具的所有电子处方，包括药品信息、用法用量等详细内容。</p>
            <div class="feature-list">
              <div class="feature-item">
                <span class="feature-icon">✅</span>
                <span>查看处方记录</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">✅</span>
                <span>药品详情和用法</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">✅</span>
                <span>按健康档案分类</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">✅</span>
                <span>用药安全提醒</span>
              </div>
            </div>
            <button @click="navigateToPrescriptions" class="intro-action-btn">
              立即查看
            </button>
          </div>
        </div>
      </div>

      <!-- 社区活动页面 -->
      <div v-else-if="activeTab === 'community'" class="page-content">
        <div class="coming-soon">
          <div class="coming-soon-icon">🌟</div>
          <h2>社区活动</h2>
          <p>此功能正在开发中，敬请期待...</p>
          <div class="feature-preview">
            <h4>即将推出的功能：</h4>
            <ul>
              <li>社区健康活动</li>
              <li>活动报名参与</li>
              <li>健康讲座信息</li>
              <li>运动健身活动</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 其他页面内容占位 -->
      <div v-else class="page-content">
        <div class="coming-soon">
          <div class="coming-soon-icon">🚧</div>
          <h2>{{ getPageTitle() }}</h2>
          <p>此功能正在开发中，敬请期待...</p>
        </div>
      </div>
    </main>

    <!-- 个人信息编辑弹窗 -->
    <div v-if="showProfileForm" class="profile-modal">
      <div class="modal-overlay" @click="hideProfileForm"></div>
      <div class="modal-content">
        <UserProfileForm
          @close="hideProfileForm"
          @success="handleProfileSuccess"
        />
      </div>
    </div>

    <!-- 密码修改弹窗 -->
    <div v-if="showPasswordForm" class="profile-modal">
      <div class="modal-overlay" @click="hidePasswordForm"></div>
      <div class="modal-content">
        <ChangePasswordForm
          @close="hidePasswordForm"
          @success="handlePasswordSuccess"
        />
      </div>
    </div>

    <!-- 成功消息 -->
    <div v-if="successMessage" class="success-message">
      {{ successMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import { getUserInfo } from '@/api/user'
import UserProfileForm from '@/components/UserProfileForm.vue'
import ChangePasswordForm from '@/components/ChangePasswordForm.vue'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const activeTab = ref('dashboard')
const pendingConsultations = ref(0) // 待处理问诊数 - 使用真实API数据
const showProfileForm = ref(false)
const showPasswordForm = ref(false)
const successMessage = ref('')
const detailedUserInfo = ref(null)

// 筛选相关数据 (暂时保留，未来功能使用)
const timeFilter = ref('all') // 时间筛选
const activityFilter = ref('all') // 活动筛选
const consultations = ref([]) // 问诊记录 - 使用真实API数据
const medicalRecords = ref([]) // 就诊记录 - 待开发
const prescriptions = ref([]) // 处方记录 - 待开发
const communityActivities = ref([]) // 社区活动 - 待开发

const userInfo = computed(() => detailedUserInfo.value || userStore.userInfo)

const roleText = computed(() => {
  if (userInfo.value?.isAdmin) return '管理员'
  if (userInfo.value?.isDoctor) return '医生'
  return '居民用户'
})

// 筛选器配置
const timeFilters = ref([
  { label: '全部', value: 'all' },
  { label: '最近一周', value: 'week' },
  { label: '最近一月', value: 'month' },
  { label: '最近三月', value: 'quarter' }
])

const activityFilters = ref([
  { label: '全部活动', value: 'all' },
  { label: '健康体检', value: 'health_check' },
  { label: '健康教育', value: 'education' },
  { label: '运动健身', value: 'exercise' }
])

// 计算属性 - 筛选后的数据
const filteredConsultations = computed(() => {
  return filterByTime(consultations.value, timeFilter.value)
})

const filteredMedicalRecords = computed(() => {
  return filterByTime(medicalRecords.value, timeFilter.value, 'visitDate')
})

const filteredPrescriptions = computed(() => {
  return filterByTime(prescriptions.value, timeFilter.value)
})

const filteredActivities = computed(() => {
  let filtered = communityActivities.value

  // 按活动类型筛选
  if (activityFilter.value !== 'all') {
    filtered = filtered.filter(activity => activity.category === activityFilter.value)
  }

  return filtered
})

// 方法
const setActiveTab = (tab) => {
  activeTab.value = tab
}

const navigateToProfile = () => {
  router.push('/profile')
}

const navigateToRecords = () => {
  router.push('/records')
}

const navigateToReminders = () => {
  router.push('/reminders')
}

const navigateToAppointments = () => {
  router.push('/appointments')
}

const navigateToConsultations = () => {
  console.log('=== 在线问诊导航调试信息 ===')
  console.log('用户信息:', userStore.userInfo)
  console.log('用户角色:', userStore.userRole)
  console.log('是否为居民:', userStore.isResident)
  console.log('是否已登录:', userStore.isLoggedIn)
  console.log('localStorage中的userInfo:', JSON.parse(localStorage.getItem('userInfo') || 'null'))
  console.log('localStorage中的token:', localStorage.getItem('token'))

  // 详细的角色检查
  if (userStore.userInfo) {
    console.log('userInfo.role:', userStore.userInfo.role)
    console.log('userInfo对象:', JSON.stringify(userStore.userInfo, null, 2))
  }

  if (!userStore.isLoggedIn) {
    alert('请先登录')
    router.push('/login')
    return
  }

  if (!userStore.isResident) {
    alert(`权限检查失败！当前角色: ${userStore.userRole}, 需要: RESIDENT`)
    console.error('权限检查失败:', {
      userRole: userStore.userRole,
      isResident: userStore.isResident,
      userInfo: userStore.userInfo
    })
    return
  }

  console.log('权限检查通过，跳转到问诊页面')
  router.push('/consultations')
}

const navigateToPrescriptions = () => {
  if (!userStore.isLoggedIn) {
    alert('请先登录')
    router.push('/login')
    return
  }

  if (!userStore.isResident) {
    alert(`权限检查失败！当前角色: ${userStore.userRole}, 需要: RESIDENT`)
    return
  }

  router.push('/prescriptions')
}

// 时间筛选方法
const filterByTime = (data, filter, dateField = 'createdAt') => {
  if (filter === 'all') return data

  const now = new Date()
  let startDate

  switch (filter) {
    case 'week':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      break
    case 'month':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      break
    case 'quarter':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
      break
    default:
      return data
  }

  return data.filter(item => {
    const itemDate = new Date(item[dateField])
    return itemDate >= startDate
  })
}

// 格式化时间方法
const formatTime = (timeString) => {
  if (!timeString) return ''
  const date = new Date(timeString)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`

  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 页面交互方法 (为未来功能预留)
const openConsultation = (consultation) => {
  // 跳转到具体的问诊详情页面
  router.push(`/consultations/${consultation.id}`)
}



const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

const getCurrentDate = () => {
  const now = new Date()
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  }
  return now.toLocaleDateString('zh-CN', options)
}

const getPageTitle = () => {
  const titles = {
    profile: '健康档案',
    appointments: '预约挂号',
    metrics: '健康数据',
    consultations: '在线问诊',
    records: '就诊记录',
    prescriptions: '查看处方',
    community: '社区活动',
    settings: '个人设置'
  }
  return titles[activeTab.value] || '页面'
}

// 加载详细用户信息
const loadDetailedUserInfo = async () => {
  try {
    const response = await getUserInfo()
    if (response.data.code === 200) {
      detailedUserInfo.value = response.data.data
    }
  } catch (error) {
    console.error('获取用户详细信息失败:', error)
  }
}

// 个人信息表单相关方法
const hideProfileForm = () => {
  showProfileForm.value = false
}

const handleProfileSuccess = async (message) => {
  hideProfileForm()
  showSuccessMessage(message)
  // 重新加载用户信息
  await loadDetailedUserInfo()
  // 同时更新store中的用户信息
  await userStore.loadUserInfo()
}

// 密码修改表单相关方法
const hidePasswordForm = () => {
  showPasswordForm.value = false
}

const handlePasswordSuccess = async (message) => {
  hidePasswordForm()
  showSuccessMessage(message)
  // 密码修改成功后，建议用户重新登录
  setTimeout(() => {
    if (confirm('密码已修改成功，为了安全起见，请重新登录。是否立即跳转到登录页面？')) {
      handleLogout()
    }
  }, 2000)
}

// 成功消息
const showSuccessMessage = (message) => {
  successMessage.value = message
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
}

const handleLogout = async () => {
  await userStore.logout()
  router.push('/login')
}

// 生命周期
onMounted(() => {
  loadDetailedUserInfo()
})
</script>

<style scoped>
.dashboard-layout {
  display: flex;
  min-height: 100vh;
  background: #f8fafc;
}

/* 左侧导航栏 */
.sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 100;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid #e2e8f0;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 28px;
}

.logo h2 {
  color: #1e293b;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  margin: 2px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.nav-item:hover {
  background: #f1f5f9;
}

.nav-item.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.nav-item.active .nav-icon {
  filter: brightness(0) invert(1);
}

.nav-icon {
  font-size: 20px;
  width: 24px;
  text-align: center;
}

.nav-item span {
  font-weight: 500;
  font-size: 15px;
}

.message-badge {
  background: #ef4444;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
  min-width: 18px;
  text-align: center;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #e2e8f0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
}

.user-role {
  color: #64748b;
  font-size: 12px;
}

.logout-btn {
  width: 100%;
  padding: 10px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #64748b;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.logout-btn:hover {
  background: #fee2e2;
  border-color: #fecaca;
  color: #dc2626;
}

.logout-icon {
  font-size: 16px;
}

/* 主内容区 */
.main-content {
  flex: 1;
  margin-left: 280px;
  padding: 24px;
  min-height: 100vh;
  background: #f8fafc;
}

.dashboard-content {
  max-width: 1200px;
}

.welcome-header {
  margin-bottom: 32px;
}

.welcome-header h1 {
  color: #1e293b;
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.date-info {
  color: #64748b;
  font-size: 16px;
  margin: 0;
}

/* 卡片网格 */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.2s ease;
}

.clickable-card {
  cursor: pointer;
}

.clickable-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h3 {
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.last-update {
  color: #64748b;
  font-size: 12px;
}

.card-content {
  padding: 20px 24px 24px;
}

/* 核心提醒卡片 */
.highlight-card {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  grid-column: span 2;
}

.highlight-card .card-header {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.highlight-card .card-header h3 {
  color: white;
}

.appointment-info {
  margin-bottom: 20px;
}

.appointment-main {
  margin-bottom: 8px;
}

.doctor-name {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
}

.appointment-time {
  font-size: 24px;
  font-weight: 700;
  color: #fbbf24;
}

.appointment-location {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.card-action-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.card-action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 健康数据卡片 */
.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.metric-item {
  text-align: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.metric-label {
  color: #64748b;
  font-size: 12px;
  margin-bottom: 8px;
}

.metric-trend {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
}

.metric-trend.up {
  background: #dcfce7;
  color: #16a34a;
}

.metric-trend.stable {
  background: #fef3c7;
  color: #d97706;
}

.mini-chart {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.chart-placeholder {
  color: #64748b;
  font-size: 14px;
}

/* 快速操作卡片 */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-radius: 12px;
  border: none;
  font-weight: 600;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-action-btn.primary {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.quick-action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.quick-action-btn.secondary {
  background: #f8fafc;
  color: #1e293b;
  border: 1px solid #e2e8f0;
}

.quick-action-btn.secondary:hover {
  background: #f1f5f9;
  transform: translateY(-2px);
}

.action-icon {
  font-size: 20px;
}

/* 最新动态卡片 */
.updates-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.update-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.update-item:hover {
  background: #f1f5f9;
}

.update-icon {
  font-size: 20px;
  margin-top: 2px;
}

.update-content {
  flex: 1;
}

.update-title {
  color: #1e293b;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
}

.update-time {
  color: #64748b;
  font-size: 12px;
}

/* 其他页面内容 */
.page-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.coming-soon {
  text-align: center;
  padding: 40px;
}

.coming-soon-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.coming-soon h2 {
  color: #1e293b;
  font-size: 24px;
  margin-bottom: 12px;
}

.coming-soon p {
  color: #64748b;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .highlight-card {
    grid-column: span 1;
  }

  .cards-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .main-content {
    margin-left: 0;
    padding: 16px;
  }

  .welcome-header h1 {
    font-size: 24px;
  }

  .cards-grid {
    gap: 16px;
  }

  .card {
    border-radius: 12px;
  }

  .card-header,
  .card-content {
    padding: 16px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    gap: 8px;
  }

  .quick-action-btn {
    padding: 12px 16px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 12px;
  }

  .welcome-header h1 {
    font-size: 20px;
  }

  .date-info {
    font-size: 14px;
  }

  .cards-grid {
    gap: 12px;
  }
}

.dashboard-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  padding: 50px 60px;
  width: 90%;
  max-width: 800px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  margin: 20px;
  max-height: 90vh;
  overflow-y: auto;
}

.welcome-section {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-section h1 {
  color: #667eea;
  font-size: 36px;
  margin-bottom: 10px;
  font-weight: 700;
}

.welcome-text {
  color: #666;
  font-size: 18px;
  margin: 0;
}

.user-info-section {
  margin-bottom: 40px;
}

.user-info-section h2 {
  color: #333;
  font-size: 24px;
  margin-bottom: 20px;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.label {
  font-weight: 600;
  color: #333;
  min-width: 80px;
}

.value {
  color: #666;
  font-weight: 500;
}

.role-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.role-badge.resident {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.role-badge.doctor {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

.role-badge.admin {
  background: linear-gradient(135deg, #FF9800, #F57C00);
}

.test-section {
  margin-bottom: 40px;
}

.test-section h2 {
  color: #333;
  font-size: 24px;
  margin-bottom: 15px;
  border-bottom: 2px solid #667eea;
  padding-bottom: 10px;
}

.test-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.feature-preview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.feature-item {
  text-align: center;
  padding: 25px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
}

.feature-icon {
  font-size: 40px;
  margin-bottom: 15px;
}

.feature-item h3 {
  color: #333;
  font-size: 18px;
  margin-bottom: 10px;
}

.feature-item p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.action-section {
  text-align: center;
}

.logout-btn {
  padding: 16px 32px;
  background: linear-gradient(135deg, #f44336, #d32f2f);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

.logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(244, 67, 54, 0.4);
  background: linear-gradient(135deg, #d32f2f, #b71c1c);
}

@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-preview {
    grid-template-columns: 1fr;
  }
  
  .dashboard-card {
    padding: 30px 40px;
  }
}

/* 个人设置页面样式 */
.settings-content {
  max-width: 1200px;
}

.settings-header {
  margin-bottom: 32px;
}

.settings-header h1 {
  color: #1e293b;
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.settings-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.settings-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f1f5f9;
  background: #fafafa;
}

.settings-card .card-header h3 {
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.edit-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-btn:hover {
  background: #096dd9;
  transform: translateY(-1px);
}

.settings-card .card-content {
  padding: 20px 24px 24px;
}

/* 个人信息预览 */
.profile-preview {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.profile-avatar {
  flex-shrink: 0;
}

.avatar-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 600;
  background-size: cover;
  background-position: center;
  border: 3px solid #f0f0f0;
}

.avatar-text {
  font-size: 24px;
  font-weight: 600;
}

.profile-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.profile-info .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
}

.profile-info .info-label {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

.profile-info .info-value {
  color: #1e293b;
  font-size: 14px;
  font-weight: 600;
}

/* 安全设置 */
.security-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.security-info {
  flex: 1;
}

.security-title {
  color: #1e293b;
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 4px;
}

.security-desc {
  color: #64748b;
  font-size: 13px;
}

.security-btn {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: not-allowed;
  transition: all 0.2s ease;
}

.security-btn.active {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
  cursor: pointer;
}

.security-btn.active:hover {
  background: #096dd9;
  border-color: #096dd9;
  transform: translateY(-1px);
}

/* 系统设置 */
.system-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.system-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.system-info {
  flex: 1;
}

.system-title {
  color: #1e293b;
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 4px;
}

.system-desc {
  color: #64748b;
  font-size: 13px;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #1890ff;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* 弹窗样式 */
.profile-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  z-index: 1001;
}

/* 成功消息 */
.success-message {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #52c41a;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 新页面样式 */
/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.page-description {
  color: #64748b;
  font-size: 16px;
  margin: 0;
}

.primary-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* 筛选器样式 */
.filter-section {
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-tabs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 8px 16px;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-tab:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.filter-tab.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* 列表容器样式 */
.consultations-list,
.records-list,
.prescriptions-list,
.activities-list {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.consultation-cards,
.record-cards,
.prescription-cards,
.activity-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

/* 卡片样式 */
.consultation-card,
.record-card,
.prescription-card,
.activity-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.consultation-card:hover,
.record-card:hover,
.prescription-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

/* 医生/医院信息样式 */
.doctor-info,
.hospital-info,
.prescription-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.doctor-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
}

.doctor-details {
  flex: 1;
}

.doctor-name,
.hospital-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.doctor-dept,
.department {
  font-size: 14px;
  color: #64748b;
}

/* 状态标签样式 */
.consultation-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.consultation-status.completed {
  background: #dcfce7;
  color: #16a34a;
}

.consultation-status.in_progress {
  background: #fef3c7;
  color: #d97706;
}

/* 卡片内容样式 */
.card-content {
  border-top: 1px solid #e2e8f0;
  padding-top: 16px;
}

.last-message {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.consultation-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #9ca3af;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.empty-state p {
  color: #64748b;
  margin: 0 0 20px 0;
}

.empty-action-btn {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.empty-action-btn:hover {
  background: #2563eb;
}

/* 功能介绍样式 */
.feature-intro {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.intro-card {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.intro-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.intro-card h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.intro-card p {
  color: #64748b;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.feature-icon {
  font-size: 16px;
  color: #10b981;
}

.intro-action-btn {
  padding: 12px 32px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.intro-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* 功能预览样式 */
.feature-preview {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-top: 24px;
  text-align: left;
}

.feature-preview h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

.feature-preview ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-preview li {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  color: #64748b;
  font-size: 14px;
}

.feature-preview li:before {
  content: "🔸";
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }

  .consultation-cards,
  .record-cards,
  .prescription-cards,
  .activity-cards {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-tabs {
    justify-content: center;
  }

  .profile-preview {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .profile-info .info-item {
    justify-content: center;
    flex-direction: column;
    gap: 4px;
  }

  .security-item,
  .system-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
