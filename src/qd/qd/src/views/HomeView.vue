<script setup>
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const userInfo = computed(() => userStore.userInfo)
</script>

<template>
  <div class="home-container">
    <div class="welcome-section">
      <h1>欢迎使用社区健康管理系统</h1>
      <p class="welcome-text">
        您好，{{ userInfo?.nickname }}！
        <span v-if="userInfo?.role === 'RESIDENT'">
          作为居民用户，您可以管理健康档案、预约医生、参与社区活动。
        </span>
        <span v-else-if="userInfo?.role === 'DOCTOR'">
          作为医生用户，您可以管理排班、接诊患者、发布健康指导。
        </span>
        <span v-else-if="userInfo?.role === 'ADMIN'">
          作为管理员，您可以管理系统用户、发布公告、维护系统数据。
        </span>
      </p>
    </div>

    <div class="features-grid">
      <div class="feature-card" v-if="userInfo?.role === 'RESIDENT'">
        <div class="feature-icon">🏥</div>
        <h3>健康档案</h3>
        <p>管理个人和家人的健康档案，记录健康数据</p>
      </div>

      <div class="feature-card" v-if="userInfo?.role === 'RESIDENT'">
        <div class="feature-icon">👨‍⚕️</div>
        <h3>预约挂号</h3>
        <p>在线预约医生，选择合适的时间就诊</p>
      </div>

      <div class="feature-card" v-if="userInfo?.role === 'RESIDENT'">
        <div class="feature-icon">💬</div>
        <h3>在线问诊</h3>
        <p>与医生在线交流，获得专业健康建议</p>
      </div>

      <div class="feature-card" v-if="userInfo?.role === 'DOCTOR'">
        <div class="feature-icon">📅</div>
        <h3>排班管理</h3>
        <p>设置您的出诊时间，管理预约患者</p>
      </div>

      <div class="feature-card" v-if="userInfo?.role === 'DOCTOR'">
        <div class="feature-icon">📋</div>
        <h3>患者管理</h3>
        <p>查看患者信息，开具电子处方</p>
      </div>

      <div class="feature-card" v-if="userInfo?.role === 'ADMIN'">
        <div class="feature-icon">⚙️</div>
        <h3>系统管理</h3>
        <p>管理用户、科室、系统配置</p>
      </div>

      <div class="feature-card">
        <div class="feature-icon">📰</div>
        <h3>健康资讯</h3>
        <p>获取最新的健康知识和社区活动信息</p>
      </div>

      <div class="feature-card">
        <div class="feature-icon">🎯</div>
        <h3>社区活动</h3>
        <p>参与社区健康活动，与邻居互动交流</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 50px;
}

.welcome-section h1 {
  color: #333;
  font-size: 32px;
  margin-bottom: 20px;
  font-weight: 600;
}

.welcome-text {
  color: #666;
  font-size: 18px;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #f0f0f0;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.feature-card h3 {
  color: #333;
  font-size: 20px;
  margin-bottom: 15px;
  font-weight: 600;
}

.feature-card p {
  color: #666;
  font-size: 16px;
  line-height: 1.5;
  margin: 0;
}

@media (max-width: 768px) {
  .home-container {
    padding: 20px 15px;
  }

  .welcome-section h1 {
    font-size: 24px;
  }

  .welcome-text {
    font-size: 16px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .feature-card {
    padding: 20px;
  }
}
</style>
