import api from './index'

/**
 * 开具电子处方（医生）
 * API测试报告确认的接口格式
 * @param {Object} data - 处方数据
 * @param {number} data.profileId - 健康档案ID
 * @param {number} data.consultationId - 问诊会话ID（可选）
 * @param {string} data.diagnosis - 诊断信息
 * @param {Array} data.medications - 药品列表
 * @returns {Promise} API响应
 */
export const createPrescription = (data) => {
  console.log('=== 开具处方API调用 ===')
  console.log('请求数据:', data)
  return api.post('/prescriptions', data)
}

/**
 * 获取患者的处方列表
 * API测试报告确认的接口格式
 * @param {number} profileId - 健康档案ID
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码（从1开始）
 * @param {number} params.size - 每页大小
 * @returns {Promise} API响应
 */
export const getPatientPrescriptions = (profileId, params = {}) => {
  console.log('=== 获取患者处方API调用 ===')
  console.log('健康档案ID:', profileId)
  console.log('查询参数:', params)

  // 确保页码从1开始（与API测试报告一致）
  const queryParams = {
    page: params.page || 1,
    size: params.size || 10,
    ...params
  }

  return api.get(`/profiles/${profileId}/prescriptions`, { params: queryParams })
}

/**
 * 获取医生开具的处方列表
 * 根据后端PrescriptionController分析，正确的API路径是 /api/prescriptions/doctor
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export const getDoctorPrescriptions = (params = {}) => {
  console.log('=== 获取医生处方列表API调用 ===')
  console.log('查询参数:', params)

  const queryParams = {
    page: params.page || 1,
    size: params.size || 10,
    ...params
  }

  // 使用正确的医生处方列表API路径
  console.log('使用API路径: /prescriptions/doctor')
  return api.get('/prescriptions/doctor', { params: queryParams })
}

/**
 * 获取单个处方详情
 * @param {number} prescriptionId - 处方ID
 * @returns {Promise} API响应
 */
export const getPrescriptionDetail = (prescriptionId) => {
  console.log('=== 获取处方详情API调用 ===')
  console.log('处方ID:', prescriptionId)
  return api.get(`/prescriptions/${prescriptionId}`)
}
