<template>
  <div class="reminder-form">
    <div class="form-header">
      <h3>{{ isEditMode ? '编辑提醒' : '创建提醒' }}</h3>
      <button @click="$emit('cancel')" class="close-btn">✕</button>
    </div>

    <form @submit.prevent="handleSubmit" class="form-content">
      <!-- 提醒标题 -->
      <div class="form-group">
        <label for="title">提醒标题 *</label>
        <input
          id="title"
          v-model="form.title"
          type="text"
          placeholder="请输入提醒标题"
          required
          maxlength="100"
          class="form-input"
        />
      </div>

      <!-- 提醒内容 -->
      <div class="form-group">
        <label for="content">提醒内容</label>
        <textarea
          id="content"
          v-model="form.content"
          placeholder="请输入提醒的详细内容"
          rows="3"
          maxlength="500"
          class="form-textarea"
        ></textarea>
      </div>

      <!-- 提醒类型 -->
      <div class="form-group">
        <label for="reminderType">提醒类型 *</label>
        <select id="reminderType" v-model="form.reminderType" required class="form-select">
          <option value="">请选择提醒类型</option>
          <option value="MEDICATION">💊 用药提醒</option>
          <option value="CHECKUP">🏥 体检提醒</option>
          <option value="EXERCISE">🏃 运动提醒</option>
          <option value="DIET">🥗 饮食提醒</option>
          <option value="MEASUREMENT">📊 测量提醒</option>
        </select>
      </div>

      <!-- 频率类型 -->
      <div class="form-group">
        <label for="frequencyType">重复频率 *</label>
        <select id="frequencyType" v-model="form.frequencyType" @change="handleFrequencyTypeChange" required class="form-select">
          <option value="">请选择重复频率</option>
          <option value="DAILY">每日</option>
          <option value="WEEKLY">每周</option>
          <option value="MONTHLY">每月</option>
          <option value="CUSTOM">自定义</option>
        </select>
      </div>

      <!-- 频率值设置 -->
      <div class="form-group" v-if="form.frequencyType">
        <label for="frequencyValue">{{ getFrequencyLabel() }} *</label>
        
        <!-- 每日频率 -->
        <div v-if="form.frequencyType === 'DAILY'" class="frequency-daily">
          <input
            v-model="dailyTime"
            type="time"
            required
            class="form-input"
          />
          <span class="help-text">每天的提醒时间</span>
        </div>

        <!-- 每周频率 -->
        <div v-else-if="form.frequencyType === 'WEEKLY'" class="frequency-weekly">
          <div class="weekly-controls">
            <select v-model="weeklyDay" required class="form-select">
              <option value="">选择星期</option>
              <option value="MONDAY">周一</option>
              <option value="TUESDAY">周二</option>
              <option value="WEDNESDAY">周三</option>
              <option value="THURSDAY">周四</option>
              <option value="FRIDAY">周五</option>
              <option value="SATURDAY">周六</option>
              <option value="SUNDAY">周日</option>
            </select>
            <input
              v-model="weeklyTime"
              type="time"
              required
              class="form-input"
            />
          </div>
          <span class="help-text">每周指定日期的提醒时间</span>
        </div>

        <!-- 每月频率 -->
        <div v-else-if="form.frequencyType === 'MONTHLY'" class="frequency-monthly">
          <div class="monthly-controls">
            <select v-model="monthlyDay" required class="form-select">
              <option value="">选择日期</option>
              <option v-for="day in 31" :key="day" :value="day">{{ day }}日</option>
            </select>
            <input
              v-model="monthlyTime"
              type="time"
              required
              class="form-input"
            />
          </div>
          <span class="help-text">每月指定日期的提醒时间</span>
        </div>

        <!-- 自定义频率 -->
        <div v-else-if="form.frequencyType === 'CUSTOM'" class="frequency-custom">
          <input
            v-model="form.frequencyValue"
            type="text"
            placeholder="请输入自定义频率值"
            required
            class="form-input"
          />
          <span class="help-text">自定义频率格式，如：每2天、每3小时等</span>
        </div>
      </div>

      <!-- 开始日期 -->
      <div class="form-group">
        <label for="startDate">开始日期</label>
        <input
          id="startDate"
          v-model="form.startDate"
          type="date"
          class="form-input"
        />
        <span class="help-text">不设置则从今天开始</span>
      </div>

      <!-- 结束日期 -->
      <div class="form-group">
        <label for="endDate">结束日期</label>
        <input
          id="endDate"
          v-model="form.endDate"
          type="date"
          :min="form.startDate"
          class="form-input"
        />
        <span class="help-text">不设置则永久有效</span>
      </div>

      <!-- 是否启用 -->
      <div class="form-group">
        <label class="checkbox-label">
          <input
            v-model="form.isActive"
            type="checkbox"
            class="form-checkbox"
          />
          <span class="checkbox-text">立即启用此提醒</span>
        </label>
      </div>

      <!-- 表单按钮 -->
      <div class="form-actions">
        <button type="button" @click="$emit('cancel')" class="btn-cancel">
          取消
        </button>
        <button type="submit" :disabled="!isFormValid" class="btn-submit">
          {{ isEditMode ? '更新提醒' : '创建提醒' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { createHealthReminder, updateHealthReminder } from '@/api/reminders'

// Props
const props = defineProps({
  reminder: {
    type: Object,
    default: null
  },
  isEditMode: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['success', 'cancel'])

// 表单数据
const form = ref({
  title: '',
  content: '',
  reminderType: '',
  frequencyType: '',
  frequencyValue: '',
  startDate: '',
  endDate: '',
  isActive: true
})

// 频率相关的辅助字段
const dailyTime = ref('')
const weeklyDay = ref('')
const weeklyTime = ref('')
const monthlyDay = ref('')
const monthlyTime = ref('')

// 计算属性
const isFormValid = computed(() => {
  return form.value.title && 
         form.value.reminderType && 
         form.value.frequencyType && 
         form.value.frequencyValue
})

// 获取频率标签
const getFrequencyLabel = () => {
  switch (form.value.frequencyType) {
    case 'DAILY': return '提醒时间'
    case 'WEEKLY': return '星期和时间'
    case 'MONTHLY': return '日期和时间'
    case 'CUSTOM': return '自定义频率'
    default: return '频率设置'
  }
}

// 处理频率类型变化
const handleFrequencyTypeChange = () => {
  // 清空频率值
  form.value.frequencyValue = ''
  dailyTime.value = ''
  weeklyDay.value = ''
  weeklyTime.value = ''
  monthlyDay.value = ''
  monthlyTime.value = ''
}

// 监听频率相关字段变化，自动构建frequencyValue
watch([dailyTime], () => {
  if (form.value.frequencyType === 'DAILY' && dailyTime.value) {
    form.value.frequencyValue = dailyTime.value
  }
})

watch([weeklyDay, weeklyTime], () => {
  if (form.value.frequencyType === 'WEEKLY' && weeklyDay.value && weeklyTime.value) {
    form.value.frequencyValue = `${weeklyDay.value},${weeklyTime.value}`
  }
})

watch([monthlyDay, monthlyTime], () => {
  if (form.value.frequencyType === 'MONTHLY' && monthlyDay.value && monthlyTime.value) {
    form.value.frequencyValue = `${monthlyDay.value},${monthlyTime.value}`
  }
})

// 初始化表单
const initForm = () => {
  if (props.isEditMode && props.reminder) {
    // 编辑模式，填充现有数据
    form.value = {
      title: props.reminder.title || '',
      content: props.reminder.content || '',
      reminderType: props.reminder.reminderType || '',
      frequencyType: props.reminder.frequencyType || '',
      frequencyValue: props.reminder.frequencyValue || '',
      startDate: props.reminder.startDate || '',
      endDate: props.reminder.endDate || '',
      isActive: props.reminder.isActive !== false
    }

    // 解析频率值到对应字段
    parseFrequencyValue()
  } else {
    // 新建模式，设置默认值
    const today = new Date().toISOString().split('T')[0]
    form.value.startDate = today
    form.value.isActive = true
  }
}

// 解析频率值
const parseFrequencyValue = () => {
  const { frequencyType, frequencyValue } = form.value
  
  if (frequencyType === 'DAILY') {
    dailyTime.value = frequencyValue
  } else if (frequencyType === 'WEEKLY' && frequencyValue.includes(',')) {
    const [day, time] = frequencyValue.split(',')
    weeklyDay.value = day
    weeklyTime.value = time
  } else if (frequencyType === 'MONTHLY' && frequencyValue.includes(',')) {
    const [day, time] = frequencyValue.split(',')
    monthlyDay.value = day
    monthlyTime.value = time
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!isFormValid.value) return

  try {
    const submitData = { ...form.value }
    
    if (props.isEditMode) {
      await updateHealthReminder(props.reminder.id, submitData)
    } else {
      await createHealthReminder(submitData)
    }
    
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    alert('操作失败，请重试')
  }
}

// 生命周期
onMounted(() => {
  initForm()
})
</script>

<style scoped>
.reminder-form {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  max-width: 600px;
  width: 100%;
}

/* 表单头部 */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.form-header h3 {
  color: #262626;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  color: #8c8c8c;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #ff4d4f;
  color: white;
}

/* 表单内容 */
.form-content {
  padding: 28px;
  max-height: 70vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  color: #262626;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 8px;
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  color: #262626;
  background: white;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.help-text {
  display: block;
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
}

/* 频率设置 */
.frequency-daily,
.frequency-weekly,
.frequency-monthly,
.frequency-custom {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.weekly-controls,
.monthly-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

/* 复选框 */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: normal !important;
}

.form-checkbox {
  width: auto !important;
  margin: 0;
  accent-color: #1890ff;
}

.checkbox-text {
  color: #262626;
  font-size: 14px;
}

/* 表单按钮 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.btn-cancel,
.btn-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-cancel {
  background: #f5f5f5;
  color: #595959;
  border: 1px solid #d9d9d9;
}

.btn-cancel:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.btn-submit {
  background: #1890ff;
  color: white;
}

.btn-submit:hover:not(:disabled) {
  background: #096dd9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.btn-submit:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
</style>
