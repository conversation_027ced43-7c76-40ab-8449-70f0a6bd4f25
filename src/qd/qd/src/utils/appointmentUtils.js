/**
 * 预约管理工具函数
 */

import { formatDate, formatTimeDisplay, isToday, isTomorrow, isPastDate, getRelativeDateDescription } from './dateUtils'

/**
 * 获取预约状态的中文显示
 * @param {string} status - 预约状态
 * @returns {string} 中文状态
 */
export const getAppointmentStatusText = (status) => {
  const statusMap = {
    'booked': '已预约',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

/**
 * 获取预约状态的CSS类名
 * @param {string} status - 预约状态
 * @returns {string} CSS类名
 */
export const getAppointmentStatusClass = (status) => {
  const classMap = {
    'booked': 'status-booked',
    'completed': 'status-completed',
    'cancelled': 'status-cancelled'
  }
  return classMap[status] || 'status-unknown'
}

/**
 * 获取预约优先级
 * @param {Object} appointment - 预约对象
 * @returns {string} 优先级 (urgent/high/normal/low)
 */
export const getAppointmentPriority = (appointment) => {
  if (!appointment.appointmentDate) return 'normal'
  
  const appointmentDate = appointment.appointmentDate
  
  // 今天的预约为紧急
  if (isToday(appointmentDate)) {
    return 'urgent'
  }
  
  // 明天的预约为高优先级
  if (isTomorrow(appointmentDate)) {
    return 'high'
  }
  
  // 过期的预约为低优先级
  if (isPastDate(appointmentDate)) {
    return 'low'
  }
  
  return 'normal'
}

/**
 * 获取优先级的CSS类名
 * @param {string} priority - 优先级
 * @returns {string} CSS类名
 */
export const getPriorityClass = (priority) => {
  const classMap = {
    'urgent': 'priority-urgent',
    'high': 'priority-high',
    'normal': 'priority-normal',
    'low': 'priority-low'
  }
  return classMap[priority] || 'priority-normal'
}

/**
 * 格式化预约时间显示
 * @param {Object} appointment - 预约对象
 * @returns {string} 格式化的时间显示
 */
export const formatAppointmentTime = (appointment) => {
  if (!appointment.appointmentDate || !appointment.appointmentTime) {
    return '时间待定'
  }
  
  const date = appointment.appointmentDate
  const time = formatTimeDisplay(appointment.appointmentTime)
  const relativeDate = getRelativeDateDescription(date)
  
  return `${relativeDate} ${time}`
}

/**
 * 格式化预约日期显示
 * @param {string} date - 日期字符串
 * @returns {string} 格式化的日期显示
 */
export const formatAppointmentDate = (date) => {
  if (!date) return '日期待定'
  
  const d = new Date(date)
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][d.getDay()]
  
  return `${year}年${month}月${day}日 ${weekday}`
}

/**
 * 检查预约是否可以操作
 * @param {Object} appointment - 预约对象
 * @param {string} action - 操作类型 (confirm/complete/cancel/edit)
 * @returns {boolean} 是否可以操作
 */
export const canOperateAppointment = (appointment, action) => {
  if (!appointment) return false
  
  const status = appointment.status
  const appointmentDate = appointment.appointmentDate
  
  switch (action) {
    case 'confirm':
      // 只有已预约状态且未过期的预约可以确认
      return status === 'booked' && !isPastDate(appointmentDate)
      
    case 'complete':
      // 只有已预约状态的预约可以完成
      return status === 'booked'
      
    case 'cancel':
      // 只有已预约状态且未过期的预约可以取消
      return status === 'booked' && !isPastDate(appointmentDate)
      
    case 'edit':
      // 只有已预约状态且未过期的预约可以编辑
      return status === 'booked' && !isPastDate(appointmentDate)
      
    case 'addRecord':
      // 已预约或已完成的预约都可以添加诊疗记录
      return status === 'booked' || status === 'completed'
      
    default:
      return false
  }
}

/**
 * 获取患者年龄
 * @param {string} birthDate - 出生日期
 * @returns {number|string} 年龄或'未知'
 */
export const getPatientAge = (birthDate) => {
  if (!birthDate) return '未知'
  
  const birth = new Date(birthDate)
  const today = new Date()
  
  if (isNaN(birth.getTime())) return '未知'
  
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  
  return age >= 0 ? age : '未知'
}

/**
 * 格式化患者信息显示
 * @param {Object} patient - 患者对象
 * @returns {string} 格式化的患者信息
 */
export const formatPatientInfo = (patient) => {
  if (!patient) return '患者信息未知'
  
  const name = patient.name || patient.profileOwnerName || '未知姓名'
  const age = getPatientAge(patient.birthDate)
  const gender = patient.gender === 'male' ? '男' : patient.gender === 'female' ? '女' : ''
  
  let info = name
  if (age !== '未知') {
    info += ` ${age}岁`
  }
  if (gender) {
    info += ` ${gender}`
  }
  
  return info
}

/**
 * 获取预约列表的筛选选项
 * @returns {Array} 筛选选项数组
 */
export const getAppointmentFilterOptions = () => {
  return [
    { value: 'all', label: '全部状态' },
    { value: 'booked', label: '已预约' },
    { value: 'completed', label: '已完成' },
    { value: 'cancelled', label: '已取消' }
  ]
}

/**
 * 获取日期筛选选项
 * @returns {Array} 日期筛选选项数组
 */
export const getDateFilterOptions = () => {
  return [
    { value: 'all', label: '全部时间' },
    { value: 'today', label: '今天' },
    { value: 'tomorrow', label: '明天' },
    { value: 'week', label: '本周' },
    { value: 'month', label: '本月' },
    { value: 'future', label: '未来' },
    { value: 'past', label: '过去' }
  ]
}

/**
 * 验证诊疗记录数据
 * @param {Object} recordData - 诊疗记录数据
 * @returns {Object} 验证结果 { isValid, errors }
 */
export const validateMedicalRecord = (recordData) => {
  const errors = {}
  
  if (!recordData.diagnosis || recordData.diagnosis.trim().length === 0) {
    errors.diagnosis = '请输入诊断结果'
  } else if (recordData.diagnosis.trim().length > 500) {
    errors.diagnosis = '诊断结果不能超过500字'
  }
  
  if (!recordData.treatment || recordData.treatment.trim().length === 0) {
    errors.treatment = '请输入治疗方案'
  } else if (recordData.treatment.trim().length > 1000) {
    errors.treatment = '治疗方案不能超过1000字'
  }
  
  if (recordData.prescription && recordData.prescription.trim().length > 1000) {
    errors.prescription = '处方信息不能超过1000字'
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

/**
 * 格式化诊疗记录显示
 * @param {Object} record - 诊疗记录对象
 * @returns {Object} 格式化的记录信息
 */
export const formatMedicalRecord = (record) => {
  if (!record) return null
  
  return {
    id: record.id,
    date: formatAppointmentDate(record.recordDate),
    diagnosis: record.diagnosis || '无',
    treatment: record.treatment || '无',
    prescription: record.prescription || '无处方',
    doctorName: record.doctorName || '未知医生',
    createdAt: record.createdAt ? new Date(record.createdAt).toLocaleString('zh-CN') : ''
  }
}

/**
 * 生成预约摘要信息
 * @param {Object} appointment - 预约对象
 * @returns {string} 预约摘要
 */
export const generateAppointmentSummary = (appointment) => {
  if (!appointment) return '预约信息不完整'
  
  const patientInfo = formatPatientInfo(appointment.patient)
  const timeInfo = formatAppointmentTime(appointment)
  const statusInfo = getAppointmentStatusText(appointment.status)
  
  return `${patientInfo} - ${timeInfo} - ${statusInfo}`
}

/**
 * 计算预约统计信息
 * @param {Array} appointments - 预约列表
 * @returns {Object} 统计信息
 */
export const calculateAppointmentStats = (appointments) => {
  if (!Array.isArray(appointments)) {
    return {
      total: 0,
      booked: 0,
      completed: 0,
      cancelled: 0,
      today: 0,
      urgent: 0
    }
  }
  
  const today = formatDate(new Date())
  
  return appointments.reduce((stats, appointment) => {
    stats.total++
    
    // 按状态统计
    if (appointment.status === 'booked') stats.booked++
    else if (appointment.status === 'completed') stats.completed++
    else if (appointment.status === 'cancelled') stats.cancelled++
    
    // 今日预约统计
    if (appointment.appointmentDate === today) stats.today++
    
    // 紧急预约统计
    const priority = getAppointmentPriority(appointment)
    if (priority === 'urgent') stats.urgent++
    
    return stats
  }, {
    total: 0,
    booked: 0,
    completed: 0,
    cancelled: 0,
    today: 0,
    urgent: 0
  })
}

/**
 * 排序预约列表
 * @param {Array} appointments - 预约列表
 * @param {string} sortBy - 排序字段 (date/time/status/priority)
 * @param {string} sortOrder - 排序顺序 (asc/desc)
 * @returns {Array} 排序后的预约列表
 */
export const sortAppointments = (appointments, sortBy = 'date', sortOrder = 'asc') => {
  if (!Array.isArray(appointments)) return []
  
  return [...appointments].sort((a, b) => {
    let compareValue = 0
    
    switch (sortBy) {
      case 'date':
        compareValue = new Date(a.appointmentDate) - new Date(b.appointmentDate)
        break
      case 'time':
        compareValue = (a.appointmentTime || '').localeCompare(b.appointmentTime || '')
        break
      case 'status':
        compareValue = (a.status || '').localeCompare(b.status || '')
        break
      case 'priority':
        const priorityOrder = { urgent: 4, high: 3, normal: 2, low: 1 }
        const aPriority = priorityOrder[getAppointmentPriority(a)] || 0
        const bPriority = priorityOrder[getAppointmentPriority(b)] || 0
        compareValue = aPriority - bPriority
        break
      default:
        compareValue = 0
    }
    
    return sortOrder === 'desc' ? -compareValue : compareValue
  })
}
