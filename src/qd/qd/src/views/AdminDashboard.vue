<template>
  <div class="admin-layout">
    <!-- 左侧导航栏 -->
    <aside class="admin-sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <div class="logo-icon">🛡️</div>
          <h2>管理控制台</h2>
        </div>
        <div class="admin-badge">
          <span class="badge-text">ADMIN</span>
        </div>
      </div>
      
      <nav class="sidebar-nav">
        <div class="nav-item active" @click="setActiveTab('overview')">
          <div class="nav-icon">📊</div>
          <span>系统概览</span>
        </div>
        <div class="nav-item" @click="setActiveTab('users')">
          <div class="nav-icon">👥</div>
          <span>用户管理</span>
        </div>
        <div class="nav-item" @click="setActiveTab('doctors')">
          <div class="nav-icon">⚕️</div>
          <span>医生管理</span>
          <div class="pending-badge" v-if="pendingDoctors > 0">{{ pendingDoctors }}</div>
        </div>
        <div class="nav-item" @click="setActiveTab('content')">
          <div class="nav-icon">📝</div>
          <span>内容管理</span>
        </div>
        <div class="nav-item" @click="setActiveTab('announcements')">
          <div class="nav-icon">📢</div>
          <span>系统公告</span>
        </div>
        <div class="nav-item" @click="setActiveTab('settings')">
          <div class="nav-icon">⚙️</div>
          <span>系统设置</span>
        </div>
      </nav>
      
      <div class="sidebar-footer">
        <div class="admin-info">
          <div class="admin-avatar">{{ userInfo?.nickname?.charAt(0) || 'A' }}</div>
          <div class="admin-details">
            <div class="admin-name">{{ userInfo?.nickname || '管理员' }}</div>
            <div class="admin-role">系统管理员</div>
            <div class="admin-status">在线</div>
          </div>
        </div>
        <button @click="handleLogout" class="logout-btn">
          <div class="logout-icon">🚪</div>
          退出系统
        </button>
      </div>
    </aside>

    <!-- 主内容区 -->
    <main class="admin-main">
      <!-- 系统概览 -->
      <div v-if="activeTab === 'overview'" class="overview-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1>系统概览</h1>
          <div class="header-actions">
            <div class="last-update">最后更新: {{ getCurrentTime() }}</div>
            <button class="refresh-btn" @click="refreshData">
              <span class="refresh-icon">🔄</span>
              刷新数据
            </button>
          </div>
        </div>

        <!-- 核心数据指标卡片 -->
        <div class="metrics-grid">
          <div class="metric-card primary">
            <div class="metric-header">
              <div class="metric-icon">👥</div>
              <div class="metric-trend up">↗ +12%</div>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ totalUsers.toLocaleString() }}</div>
              <div class="metric-label">总注册用户</div>
              <div class="metric-detail">本月新增 {{ newUsersThisMonth }} 人</div>
            </div>
          </div>

          <div class="metric-card success">
            <div class="metric-header">
              <div class="metric-icon">⚕️</div>
              <div class="metric-trend up">↗ +3</div>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ verifiedDoctors }}</div>
              <div class="metric-label">认证医生数</div>
              <div class="metric-detail">待审核 {{ pendingDoctors }} 人</div>
            </div>
          </div>

          <div class="metric-card warning">
            <div class="metric-header">
              <div class="metric-icon">🔥</div>
              <div class="metric-trend stable">→ 稳定</div>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ activeUsersToday }}</div>
              <div class="metric-label">今日活跃用户</div>
              <div class="metric-detail">在线用户 {{ onlineUsers }} 人</div>
            </div>
          </div>

          <div class="metric-card info">
            <div class="metric-header">
              <div class="metric-icon">📅</div>
              <div class="metric-trend up">↗ +8%</div>
            </div>
            <div class="metric-content">
              <div class="metric-number">{{ totalAppointments.toLocaleString() }}</div>
              <div class="metric-label">平台总预约量</div>
              <div class="metric-detail">今日预约 {{ todayAppointments }} 次</div>
            </div>
          </div>
        </div>

        <!-- 系统状态和待办事项 -->
        <div class="dashboard-grid">
          <!-- 系统健康状态 -->
          <div class="system-health-card">
            <div class="card-header">
              <h3>🖥️ 系统健康状态</h3>
              <div class="health-status" :class="systemHealth.status">
                {{ systemHealth.text }}
              </div>
            </div>
            <div class="card-content">
              <div class="health-metrics">
                <div class="health-item">
                  <div class="health-label">服务器负载</div>
                  <div class="health-value">
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: systemHealth.serverLoad + '%' }"></div>
                    </div>
                    <span>{{ systemHealth.serverLoad }}%</span>
                  </div>
                </div>
                <div class="health-item">
                  <div class="health-label">数据库连接</div>
                  <div class="health-value">
                    <span class="status-dot" :class="systemHealth.dbStatus"></span>
                    {{ systemHealth.dbStatus === 'healthy' ? '正常' : '异常' }}
                  </div>
                </div>
                <div class="health-item">
                  <div class="health-label">API响应时间</div>
                  <div class="health-value">{{ systemHealth.apiResponseTime }}ms</div>
                </div>
                <div class="health-item">
                  <div class="health-label">存储空间</div>
                  <div class="health-value">
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: systemHealth.storageUsage + '%' }"></div>
                    </div>
                    <span>{{ systemHealth.storageUsage }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 待办事项表格 -->
          <div class="pending-tasks-card">
            <div class="card-header">
              <h3>⚡ 待办事项</h3>
              <div class="task-summary">{{ pendingTasks.length }} 项待处理</div>
            </div>
            <div class="card-content">
              <div class="tasks-table">
                <div class="table-header">
                  <div class="col-type">类型</div>
                  <div class="col-content">内容</div>
                  <div class="col-time">提交时间</div>
                  <div class="col-priority">优先级</div>
                  <div class="col-actions">操作</div>
                </div>
                <div v-for="task in pendingTasks" :key="task.id" class="table-row" :class="task.priority">
                  <div class="col-type">
                    <span class="task-type-badge" :class="task.type">{{ getTaskTypeText(task.type) }}</span>
                  </div>
                  <div class="col-content">
                    <div class="task-title">{{ task.title }}</div>
                    <div class="task-subtitle">{{ task.subtitle }}</div>
                  </div>
                  <div class="col-time">{{ task.submitTime }}</div>
                  <div class="col-priority">
                    <span class="priority-badge" :class="task.priority">{{ getPriorityText(task.priority) }}</span>
                  </div>
                  <div class="col-actions">
                    <button class="action-btn approve" @click="approveTask(task)" v-if="task.type === 'doctor-review'">
                      批准
                    </button>
                    <button class="action-btn reject" @click="rejectTask(task)" v-if="task.type === 'doctor-review'">
                      拒绝
                    </button>
                    <button class="action-btn edit" @click="editTask(task)" v-if="task.type === 'content-review'">
                      编辑发布
                    </button>
                    <button class="action-btn view" @click="viewTask(task)">
                      查看详情
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据图表区域 -->
        <div class="charts-section">
          <div class="chart-card">
            <div class="chart-header">
              <h3>📈 用户增长趋势</h3>
              <div class="chart-controls">
                <select class="time-selector">
                  <option value="7d">最近7天</option>
                  <option value="30d">最近30天</option>
                  <option value="90d">最近90天</option>
                </select>
              </div>
            </div>
            <div class="chart-content">
              <div class="chart-placeholder">
                📊 用户增长趋势图表
                <div class="chart-note">集成 ECharts 后显示真实数据</div>
              </div>
            </div>
          </div>

          <div class="chart-card">
            <div class="chart-header">
              <h3>🏥 预约统计分析</h3>
              <div class="chart-controls">
                <select class="dept-selector">
                  <option value="all">全部科室</option>
                  <option value="internal">内科</option>
                  <option value="surgery">外科</option>
                  <option value="pediatrics">儿科</option>
                </select>
              </div>
            </div>
            <div class="chart-content">
              <div class="chart-placeholder">
                📊 预约量统计图表
                <div class="chart-note">按科室分类的预约数据分析</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 其他页面内容占位 -->
      <div v-else class="page-content">
        <div class="coming-soon">
          <div class="coming-soon-icon">🚧</div>
          <h2>{{ getPageTitle() }}</h2>
          <p>此功能正在开发中，敬请期待...</p>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'

const userStore = useUserStore()
const router = useRouter()

// 响应式数据
const activeTab = ref('overview')

// 模拟数据
const totalUsers = ref(15847)
const newUsersThisMonth = ref(1256)
const verifiedDoctors = ref(89)
const pendingDoctors = ref(5)
const activeUsersToday = ref(2341)
const onlineUsers = ref(156)
const totalAppointments = ref(45623)
const todayAppointments = ref(127)

const systemHealth = ref({
  status: 'healthy',
  text: '系统运行正常',
  serverLoad: 35,
  dbStatus: 'healthy',
  apiResponseTime: 145,
  storageUsage: 67
})

const pendingTasks = ref([
  {
    id: 1,
    type: 'doctor-review',
    title: '医生资质审核',
    subtitle: '李医生 - 心内科主治医师',
    submitTime: '2小时前',
    priority: 'high'
  },
  {
    id: 2,
    type: 'content-review',
    title: '健康资讯审核',
    subtitle: '春季养生保健指南',
    submitTime: '4小时前',
    priority: 'medium'
  },
  {
    id: 3,
    type: 'doctor-review',
    title: '医生资质审核',
    subtitle: '张医生 - 儿科副主任医师',
    submitTime: '1天前',
    priority: 'medium'
  },
  {
    id: 4,
    type: 'user-report',
    title: '用户举报处理',
    subtitle: '不当言论举报',
    submitTime: '2天前',
    priority: 'low'
  }
])

const userInfo = computed(() => userStore.userInfo)

// 方法
const setActiveTab = (tab) => {
  activeTab.value = tab
}

const getCurrentTime = () => {
  return new Date().toLocaleString('zh-CN')
}

const refreshData = () => {
  console.log('刷新数据...')
  // 这里将来连接API刷新数据
}

const getPageTitle = () => {
  const titles = {
    users: '用户管理',
    doctors: '医生管理',
    content: '内容管理',
    announcements: '系统公告',
    settings: '系统设置'
  }
  return titles[activeTab.value] || '页面'
}

const getTaskTypeText = (type) => {
  const typeMap = {
    'doctor-review': '医生审核',
    'content-review': '内容审核',
    'user-report': '用户举报'
  }
  return typeMap[type] || type
}

const getPriorityText = (priority) => {
  const priorityMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return priorityMap[priority] || priority
}

const approveTask = (task) => {
  console.log('批准任务:', task)
  // 这里将来连接API
}

const rejectTask = (task) => {
  console.log('拒绝任务:', task)
  // 这里将来连接API
}

const editTask = (task) => {
  console.log('编辑任务:', task)
  // 这里将来连接API
}

const viewTask = (task) => {
  console.log('查看任务详情:', task)
  // 这里将来连接API
}

const handleLogout = async () => {
  await userStore.logout()
  router.push('/login')
}
</script>

<style scoped>
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: #f0f2f5;
}

/* 管理员侧边栏 - 深蓝色权威主题 */
.admin-sidebar {
  width: 320px;
  background: linear-gradient(180deg, #001529 0%, #002140 50%, #001529 100%);
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 100;
  box-shadow: 6px 0 24px rgba(0, 0, 0, 0.15);
}

.sidebar-header {
  padding: 28px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.logo {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.logo-icon {
  font-size: 36px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.logo h2 {
  color: white;
  font-size: 22px;
  font-weight: 700;
  margin: 0;
  letter-spacing: 0.5px;
}

.admin-badge {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
  border: 1px solid rgba(255, 77, 79, 0.3);
  padding: 6px 16px;
  border-radius: 16px;
  display: inline-block;
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
}

.badge-text {
  color: white;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 1px;
}

.sidebar-nav {
  flex: 1;
  padding: 24px 0;
  overflow-y: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  margin: 3px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  font-weight: 500;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(4px);
}

.nav-item.active {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
  transform: translateX(8px);
}

.nav-icon {
  font-size: 22px;
  width: 28px;
  text-align: center;
}

.nav-item span {
  font-size: 16px;
  letter-spacing: 0.3px;
}

.pending-badge {
  background: #ff4d4f;
  color: white;
  font-size: 11px;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 14px;
  margin-left: auto;
  min-width: 22px;
  text-align: center;
  animation: glow 2s infinite;
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 8px rgba(255, 77, 79, 0.6); }
  50% { box-shadow: 0 0 16px rgba(255, 77, 79, 0.8); }
}

.sidebar-footer {
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.admin-avatar {
  width: 52px;
  height: 52px;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.admin-details {
  flex: 1;
}

.admin-name {
  font-weight: 700;
  color: white;
  font-size: 16px;
  margin-bottom: 4px;
}

.admin-role {
  color: #8c8c8c;
  font-size: 13px;
  margin-bottom: 2px;
}

.admin-status {
  color: #52c41a;
  font-size: 12px;
  font-weight: 600;
}

.logout-btn {
  width: 100%;
  padding: 14px 20px;
  background: rgba(255, 77, 79, 0.1);
  border: 1px solid rgba(255, 77, 79, 0.2);
  border-radius: 10px;
  color: #ff7875;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
}

.logout-btn:hover {
  background: rgba(255, 77, 79, 0.2);
  border-color: rgba(255, 77, 79, 0.4);
  color: #ff4d4f;
  transform: translateY(-2px);
}

.logout-icon {
  font-size: 18px;
}

/* 主内容区 */
.admin-main {
  flex: 1;
  margin-left: 320px;
  padding: 28px;
  min-height: 100vh;
  background: #f0f2f5;
}

.overview-content {
  max-width: 1600px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e8e8e8;
}

.page-header h1 {
  color: #262626;
  font-size: 36px;
  font-weight: 700;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.last-update {
  color: #8c8c8c;
  font-size: 14px;
}

.refresh-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-btn:hover {
  background: #096dd9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.refresh-icon {
  font-size: 16px;
}

/* 核心数据指标卡片 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.metric-card {
  background: white;
  border-radius: 16px;
  padding: 28px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, transparent, currentColor, transparent);
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.metric-card.primary {
  color: #1890ff;
}

.metric-card.success {
  color: #52c41a;
}

.metric-card.warning {
  color: #faad14;
}

.metric-card.info {
  color: #722ed1;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.metric-icon {
  font-size: 32px;
  padding: 12px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 12px;
}

.metric-card.success .metric-icon {
  background: rgba(82, 196, 26, 0.1);
}

.metric-card.warning .metric-icon {
  background: rgba(250, 173, 20, 0.1);
}

.metric-card.info .metric-icon {
  background: rgba(114, 46, 209, 0.1);
}

.metric-trend {
  font-size: 14px;
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.metric-trend.up {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.metric-trend.stable {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.metric-content {
  text-align: left;
}

.metric-number {
  font-size: 42px;
  font-weight: 700;
  color: #262626;
  margin-bottom: 8px;
  line-height: 1;
}

.metric-label {
  color: #595959;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.metric-detail {
  color: #8c8c8c;
  font-size: 14px;
  font-weight: 500;
}

/* 仪表盘网格 */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 24px;
  margin-bottom: 32px;
}

.system-health-card,
.pending-tasks-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.card-header {
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.card-header h3 {
  color: #262626;
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}

.health-status {
  padding: 6px 16px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 600;
}

.health-status.healthy {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.task-summary {
  background: #1890ff;
  color: white;
  padding: 6px 16px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 600;
}

.card-content {
  padding: 24px 28px;
}

/* 系统健康状态 */
.health-metrics {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 10px;
  border-left: 4px solid #1890ff;
}

.health-label {
  color: #595959;
  font-weight: 600;
  font-size: 14px;
}

.health-value {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #262626;
}

.progress-bar {
  width: 80px;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #1890ff);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.healthy {
  background: #52c41a;
  box-shadow: 0 0 8px rgba(82, 196, 26, 0.5);
}

/* 待办事项表格 */
.tasks-table {
  width: 100%;
}

.table-header {
  display: grid;
  grid-template-columns: 100px 2fr 120px 80px 140px;
  gap: 16px;
  padding: 16px 20px;
  background: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 12px;
  font-weight: 700;
  font-size: 13px;
  color: #595959;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-row {
  display: grid;
  grid-template-columns: 100px 2fr 120px 80px 140px;
  gap: 16px;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
  transition: all 0.2s ease;
}

.table-row:hover {
  background: #fafafa;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row.high {
  border-left: 4px solid #ff4d4f;
  background: rgba(255, 77, 79, 0.02);
}

.table-row.medium {
  border-left: 4px solid #faad14;
}

.table-row.low {
  border-left: 4px solid #52c41a;
}

.task-type-badge {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.task-type-badge.doctor-review {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.task-type-badge.content-review {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.task-type-badge.user-report {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.task-title {
  font-weight: 600;
  color: #262626;
  font-size: 14px;
  margin-bottom: 4px;
}

.task-subtitle {
  color: #8c8c8c;
  font-size: 13px;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  text-align: center;
}

.priority-badge.high {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.priority-badge.medium {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.priority-badge.low {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.col-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 6px;
  border: none;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn.approve {
  background: #52c41a;
  color: white;
}

.action-btn.approve:hover {
  background: #389e0d;
  transform: translateY(-1px);
}

.action-btn.reject {
  background: #ff4d4f;
  color: white;
}

.action-btn.reject:hover {
  background: #d9363e;
  transform: translateY(-1px);
}

.action-btn.edit {
  background: #1890ff;
  color: white;
}

.action-btn.edit:hover {
  background: #096dd9;
  transform: translateY(-1px);
}

.action-btn.view {
  background: #f0f0f0;
  color: #595959;
}

.action-btn.view:hover {
  background: #d9d9d9;
  color: #262626;
}

/* 图表区域 */
.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.chart-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.chart-header {
  padding: 24px 28px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.chart-header h3 {
  color: #262626;
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}

.chart-controls select {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 13px;
  color: #595959;
  cursor: pointer;
}

.chart-content {
  padding: 40px 28px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #8c8c8c;
  font-size: 18px;
  font-weight: 600;
}

.chart-note {
  color: #bfbfbf;
  font-size: 14px;
  font-weight: 400;
  margin-top: 12px;
}

/* 其他页面内容 */
.page-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
}

.coming-soon {
  text-align: center;
  padding: 60px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.coming-soon-icon {
  font-size: 80px;
  margin-bottom: 24px;
}

.coming-soon h2 {
  color: #262626;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 16px;
}

.coming-soon p {
  color: #8c8c8c;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .charts-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .table-header,
  .table-row {
    grid-template-columns: 80px 1fr 100px 60px 120px;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .admin-main {
    margin-left: 0;
    padding: 20px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .page-header h1 {
    font-size: 28px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .metrics-grid {
    gap: 16px;
  }

  .metric-card {
    padding: 20px;
  }

  .dashboard-grid {
    gap: 16px;
  }

  .table-header {
    display: none;
  }

  .table-row {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;
    margin-bottom: 12px;
    border: none;
  }

  .col-actions {
    justify-content: flex-start;
  }

  .health-metrics {
    gap: 16px;
  }

  .health-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .admin-main {
    padding: 16px;
  }

  .metric-card {
    padding: 16px;
  }

  .metric-number {
    font-size: 32px;
  }

  .card-content {
    padding: 16px 20px;
  }

  .chart-content {
    padding: 24px 20px;
    min-height: 200px;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 11px;
  }
}
</style>
