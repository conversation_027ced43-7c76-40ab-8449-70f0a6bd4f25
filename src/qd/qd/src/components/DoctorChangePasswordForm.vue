<template>
  <div class="doctor-password-form-overlay">
    <div class="doctor-password-form-container">
      <div class="form-header">
        <h3>🔒 修改密码</h3>
        <button @click="$emit('close')" class="close-btn">✕</button>
      </div>

      <div class="form-content">
        <form @submit.prevent="handleSubmit" class="password-form">
          <!-- 当前密码 -->
          <div class="form-group">
            <label for="currentPassword">当前密码 *</label>
            <div class="password-input-wrapper">
              <input
                id="currentPassword"
                v-model="form.currentPassword"
                :type="showCurrentPassword ? 'text' : 'password'"
                placeholder="请输入当前密码"
                required
                class="form-input"
                :class="{ 'error': errors.currentPassword }"
              />
              <button
                type="button"
                @click="showCurrentPassword = !showCurrentPassword"
                class="password-toggle"
              >
                {{ showCurrentPassword ? '🙈' : '👁️' }}
              </button>
            </div>
            <span v-if="errors.currentPassword" class="error-text">{{ errors.currentPassword }}</span>
          </div>

          <!-- 新密码 -->
          <div class="form-group">
            <label for="newPassword">新密码 *</label>
            <div class="password-input-wrapper">
              <input
                id="newPassword"
                v-model="form.newPassword"
                :type="showNewPassword ? 'text' : 'password'"
                placeholder="请输入新密码 (6-20位)"
                required
                minlength="6"
                maxlength="20"
                class="form-input"
                :class="{ 'error': errors.newPassword }"
              />
              <button
                type="button"
                @click="showNewPassword = !showNewPassword"
                class="password-toggle"
              >
                {{ showNewPassword ? '🙈' : '👁️' }}
              </button>
            </div>
            <span v-if="errors.newPassword" class="error-text">{{ errors.newPassword }}</span>
            <span class="help-text">密码长度6-20位，建议包含字母和数字</span>
          </div>

          <!-- 确认新密码 -->
          <div class="form-group">
            <label for="confirmPassword">确认新密码 *</label>
            <div class="password-input-wrapper">
              <input
                id="confirmPassword"
                v-model="form.confirmPassword"
                :type="showConfirmPassword ? 'text' : 'password'"
                placeholder="请再次输入新密码"
                required
                class="form-input"
                :class="{ 'error': errors.confirmPassword }"
              />
              <button
                type="button"
                @click="showConfirmPassword = !showConfirmPassword"
                class="password-toggle"
              >
                {{ showConfirmPassword ? '🙈' : '👁️' }}
              </button>
            </div>
            <span v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</span>
          </div>

          <!-- 密码强度指示器 -->
          <div v-if="form.newPassword" class="password-strength">
            <div class="strength-label">密码强度:</div>
            <div class="strength-bar">
              <div 
                class="strength-fill" 
                :class="passwordStrengthClass"
                :style="{ width: passwordStrengthPercent + '%' }"
              ></div>
            </div>
            <span class="strength-text" :class="passwordStrengthClass">
              {{ passwordStrengthText }}
            </span>
          </div>

          <!-- 表单按钮 -->
          <div class="form-actions">
            <button type="button" @click="resetForm" class="btn-reset">
              重置
            </button>
            <button type="submit" :disabled="!isFormValid || loading" class="btn-submit">
              <span v-if="loading">修改中...</span>
              <span v-else>确认修改</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { changeDoctorPassword } from '@/api/user'

// Props & Emits
const emit = defineEmits(['close', 'success'])

// 响应式数据
const loading = ref(false)
const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)

const form = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const errors = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 计算属性
const isFormValid = computed(() => {
  return form.value.currentPassword &&
         form.value.newPassword &&
         form.value.confirmPassword &&
         form.value.newPassword.length >= 6 &&
         form.value.newPassword.length <= 20 &&
         form.value.newPassword === form.value.confirmPassword &&
         form.value.newPassword !== form.value.currentPassword
})

// 密码强度计算
const passwordStrength = computed(() => {
  const password = form.value.newPassword
  if (!password) return 0
  
  let score = 0
  
  // 长度检查
  if (password.length >= 8) score += 25
  if (password.length >= 12) score += 25
  
  // 包含数字
  if (/\d/.test(password)) score += 25
  
  // 包含字母
  if (/[a-zA-Z]/.test(password)) score += 25
  
  return score
})

const passwordStrengthPercent = computed(() => passwordStrength.value)
const passwordStrengthClass = computed(() => {
  const strength = passwordStrength.value
  if (strength < 25) return 'weak'
  if (strength < 50) return 'fair'
  if (strength < 75) return 'good'
  return 'strong'
})

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value
  if (strength < 25) return '弱'
  if (strength < 50) return '一般'
  if (strength < 75) return '良好'
  return '强'
})

// 监听表单变化，清除错误信息
watch(() => form.value.newPassword, () => {
  errors.value.newPassword = ''
  if (form.value.confirmPassword && form.value.newPassword !== form.value.confirmPassword) {
    errors.value.confirmPassword = '两次输入的密码不一致'
  } else {
    errors.value.confirmPassword = ''
  }
})

watch(() => form.value.confirmPassword, () => {
  errors.value.confirmPassword = ''
  if (form.value.newPassword && form.value.newPassword !== form.value.confirmPassword) {
    errors.value.confirmPassword = '两次输入的密码不一致'
  }
})

watch(() => form.value.currentPassword, () => {
  errors.value.currentPassword = ''
})

// 方法
const validateForm = () => {
  let isValid = true
  errors.value = { currentPassword: '', newPassword: '', confirmPassword: '' }

  // 验证当前密码
  if (!form.value.currentPassword) {
    errors.value.currentPassword = '请输入当前密码'
    isValid = false
  }

  // 验证新密码
  if (!form.value.newPassword) {
    errors.value.newPassword = '请输入新密码'
    isValid = false
  } else if (form.value.newPassword.length < 6 || form.value.newPassword.length > 20) {
    errors.value.newPassword = '密码长度必须在6-20位之间'
    isValid = false
  } else if (form.value.newPassword === form.value.currentPassword) {
    errors.value.newPassword = '新密码不能与当前密码相同'
    isValid = false
  }

  // 验证确认密码
  if (!form.value.confirmPassword) {
    errors.value.confirmPassword = '请确认新密码'
    isValid = false
  } else if (form.value.newPassword !== form.value.confirmPassword) {
    errors.value.confirmPassword = '两次输入的密码不一致'
    isValid = false
  }

  return isValid
}

const resetForm = () => {
  form.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
  errors.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  }
  showCurrentPassword.value = false
  showNewPassword.value = false
  showConfirmPassword.value = false
}

const handleSubmit = async () => {
  if (!validateForm()) return

  loading.value = true
  try {
    const response = await changeDoctorPassword(form.value)
    
    if (response.data.code === 200) {
      emit('success', '密码修改成功！请重新登录。')
      resetForm()
    } else {
      errors.value.currentPassword = response.data.message || '密码修改失败'
    }
  } catch (error) {
    console.error('密码修改失败:', error)
    if (error.response?.status === 400) {
      errors.value.currentPassword = '当前密码错误'
    } else {
      errors.value.currentPassword = '密码修改失败，请重试'
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.doctor-password-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.doctor-password-form-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12px 12px 0 0;
}

.form-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.form-content {
  padding: 24px;
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 12px 40px 12px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: #ef4444;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.password-toggle:hover {
  background: #f3f4f6;
}

.help-text {
  font-size: 12px;
  color: #6b7280;
}

.error-text {
  font-size: 12px;
  color: #ef4444;
}

.password-strength {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
}

.strength-label {
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
}

.strength-bar {
  flex: 1;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: width 0.3s, background-color 0.3s;
}

.strength-fill.weak {
  background: #ef4444;
}

.strength-fill.fair {
  background: #f59e0b;
}

.strength-fill.good {
  background: #10b981;
}

.strength-fill.strong {
  background: #059669;
}

.strength-text {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.strength-text.weak {
  color: #ef4444;
}

.strength-text.fair {
  color: #f59e0b;
}

.strength-text.good {
  color: #10b981;
}

.strength-text.strong {
  color: #059669;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 8px;
}

.btn-reset,
.btn-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-reset {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-reset:hover {
  background: #e5e7eb;
}

.btn-submit {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
}

.btn-submit:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-submit:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
</style>
